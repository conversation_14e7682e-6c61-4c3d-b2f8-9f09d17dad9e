<?php
/**
 * Test email sender for QuickTempMail WP
 * This script helps test email receiving functionality
 */

// Simple HTML form to send test emails
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Test Email - QuickTempMail WP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="email"], input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        textarea {
            height: 120px;
            resize: vertical;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Send Test Email</h1>
        
        <div class="instructions">
            <h3>📋 Instructions (বাংলায়)</h3>
            <ol>
                <li><strong>QuickTempMail plugin থেকে email address copy করুন</strong></li>
                <li><strong>নিচের form এ সেই email address paste করুন</strong></li>
                <li><strong>Subject এবং message লিখুন</strong></li>
                <li><strong>"Send Test Email" button click করুন</strong></li>
                <li><strong>QuickTempMail inbox এ email আসার জন্য অপেক্ষা করুন</strong></li>
            </ol>
        </div>

        <form id="emailForm">
            <div class="form-group">
                <label for="to_email">To Email Address:</label>
                <input type="email" id="to_email" name="to_email" required 
                       placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="subject">Subject:</label>
                <input type="text" id="subject" name="subject" required 
                       value="Test Email from QuickTempMail WP">
            </div>
            
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message" name="message" required>Hello! This is a test email to verify that your QuickTempMail WP plugin is working correctly.

If you can see this message in your temporary inbox, then the email receiving functionality is working perfectly!

Time sent: <?php echo date('Y-m-d H:i:s'); ?>

Best regards,
QuickTempMail Test System</textarea>
            </div>
            
            <button type="submit" class="btn">Send Test Email</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <div class="instructions" style="margin-top: 30px;">
            <h3>🔧 Alternative Testing Methods</h3>
            <p><strong>যদি এই form কাজ না করে:</strong></p>
            <ul>
                <li><strong>Gmail/Yahoo থেকে manual email পাঠান</strong></li>
                <li><strong>Online email testing service ব্যবহার করুন</strong></li>
                <li><strong>অন্য কোন email client থেকে test email পাঠান</strong></li>
            </ul>
            
            <h3>📱 Quick Test Services</h3>
            <ul>
                <li><a href="https://mail-tester.com" target="_blank">Mail-Tester.com</a></li>
                <li><a href="https://www.mail7.io" target="_blank">Mail7.io</a></li>
                <li><a href="https://temp-mail.org" target="_blank">Temp-Mail.org</a> (for comparison)</li>
            </ul>
        </div>
    </div>

    <script>
        document.getElementById('emailForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            const toEmail = document.getElementById('to_email').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value;
            
            // Show loading
            resultDiv.className = 'result info';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '📤 Sending email... Please wait.';
            
            // Simulate email sending (this is just a demo)
            setTimeout(() => {
                // Check if it's a valid temporary email domain
                const tempDomains = ['1secmail.com', '1secmail.org', '1secmail.net', 'esiix.com', 'guerrillamail.com', 'tempmail.org'];
                const emailDomain = toEmail.split('@')[1];
                
                if (tempDomains.includes(emailDomain)) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Test Email Prepared!</strong><br>
                        <strong>To:</strong> ${toEmail}<br>
                        <strong>Subject:</strong> ${subject}<br>
                        <br>
                        <em>Note: This is a demo form. To actually send emails, use a real email client like Gmail, Yahoo, or Outlook.</em>
                        <br><br>
                        <strong>Next Steps:</strong>
                        <ol>
                            <li>Copy the email address: <code>${toEmail}</code></li>
                            <li>Open your Gmail/Yahoo/Outlook</li>
                            <li>Send an email to this address</li>
                            <li>Check QuickTempMail inbox for the email</li>
                        </ol>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>⚠️ Warning:</strong> ${emailDomain} may not be a temporary email domain.
                        <br><br>
                        <strong>Supported domains:</strong>
                        <ul>
                            <li>1secmail.com</li>
                            <li>1secmail.org</li>
                            <li>1secmail.net</li>
                            <li>esiix.com</li>
                        </ul>
                    `;
                }
            }, 2000);
        });
        
        // Auto-fill if email is in URL
        const urlParams = new URLSearchParams(window.location.search);
        const emailParam = urlParams.get('email');
        if (emailParam) {
            document.getElementById('to_email').value = emailParam;
        }
    </script>
</body>
</html>

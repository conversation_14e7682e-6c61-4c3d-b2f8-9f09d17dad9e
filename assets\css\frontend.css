/**
 * QuickTempMail WP Frontend Styles
 */

/* Container */
.quicktempmail-container {
    max-width: 800px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Email Section */
.quicktempmail-email-section {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.quicktempmail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.quicktempmail-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.quicktempmail-timer {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #6c757d;
}

.timer-value {
    font-weight: 600;
    color: #28a745;
    font-family: 'Courier New', monospace;
}

.timer-value.warning {
    color: #ffc107;
}

.timer-value.danger {
    color: #dc3545;
}

/* Email Display */
.quicktempmail-email-display {
    margin-top: 15px;
}

.email-input-group {
    display: flex;
    gap: 10px;
    align-items: stretch;
    flex-wrap: wrap;
}

.quicktempmail-email-input {
    flex: 1;
    min-width: 250px;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 16px;
    font-family: 'Courier New', monospace;
    background: #ffffff;
    color: #2c3e50;
    transition: border-color 0.3s ease;
}

.quicktempmail-email-input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.email-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quicktempmail-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
}

.quicktempmail-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.quicktempmail-btn:active {
    transform: translateY(0);
}

.quicktempmail-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.quicktempmail-generate-btn {
    background: #007cba;
    color: white;
}

.quicktempmail-generate-btn:hover {
    background: #005a87;
}

.quicktempmail-copy-btn {
    background: #28a745;
    color: white;
}

.quicktempmail-copy-btn:hover {
    background: #1e7e34;
}

.quicktempmail-copy-btn.copied {
    background: #17a2b8;
}

.quicktempmail-refresh-btn {
    background: #6c757d;
    color: white;
}

.quicktempmail-refresh-btn:hover {
    background: #545b62;
}

.quicktempmail-delete-btn {
    background: #dc3545;
    color: white;
}

.quicktempmail-delete-btn:hover {
    background: #c82333;
}

/* Status Messages */
.quicktempmail-status {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    display: none;
}

.quicktempmail-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.quicktempmail-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.quicktempmail-status.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    display: block;
}

/* Inbox Section */
.quicktempmail-inbox-section {
    padding: 20px;
}

.quicktempmail-inbox-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.quicktempmail-inbox-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.inbox-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
    color: #6c757d;
}

.message-count {
    font-weight: 500;
}

.auto-refresh-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.refresh-dot {
    width: 8px;
    height: 8px;
    background: #28a745;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* Inbox Content */
.quicktempmail-inbox-content {
    position: relative;
    min-height: 200px;
}

.inbox-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 40px;
    color: #6c757d;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e1e5e9;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.inbox-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.inbox-empty p {
    margin: 8px 0;
}

.empty-subtitle {
    font-size: 14px;
    opacity: 0.8;
}

/* Messages Table */
.quicktempmail-messages-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.quicktempmail-messages-table th,
.quicktempmail-messages-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.quicktempmail-messages-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.quicktempmail-messages-table td {
    font-size: 14px;
    color: #495057;
}

.quicktempmail-messages-table tbody tr {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.quicktempmail-messages-table tbody tr:hover {
    background: #f8f9fa;
}

.message-from {
    font-weight: 500;
    color: #2c3e50;
}

.message-subject {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.message-time {
    color: #6c757d;
    font-size: 13px;
    white-space: nowrap;
}

.message-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.view {
    background: #007cba;
    color: white;
}

.action-btn.delete {
    background: #dc3545;
    color: white;
}

.action-btn:hover {
    opacity: 0.8;
}

/* Modal */
.quicktempmail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    position: relative;
    width: 90%;
    max-width: 700px;
    max-height: 80vh;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #2c3e50;
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.email-headers {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.header-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.header-row:last-child {
    margin-bottom: 0;
}

.header-row strong {
    min-width: 60px;
    color: #2c3e50;
}

.header-row span {
    color: #495057;
    word-break: break-all;
}

.email-content {
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    overflow: hidden;
}

.content-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.tab-btn {
    padding: 10px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    color: #6c757d;
    transition: all 0.2s ease;
}

.tab-btn.active {
    background: white;
    color: #2c3e50;
    font-weight: 500;
}

.tab-btn:hover {
    background: #e9ecef;
}

.content-body {
    position: relative;
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
}

.content-html,
.content-text {
    padding: 20px;
    display: none;
}

.content-html.active,
.content-text.active {
    display: block;
}

.content-text {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .quicktempmail-container {
        margin: 10px;
        border-radius: 6px;
    }
    
    .quicktempmail-email-section,
    .quicktempmail-inbox-section {
        padding: 15px;
    }
    
    .quicktempmail-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .email-input-group {
        flex-direction: column;
    }
    
    .quicktempmail-email-input {
        min-width: auto;
    }
    
    .email-buttons {
        justify-content: stretch;
    }
    
    .quicktempmail-btn {
        flex: 1;
        justify-content: center;
    }
    
    .quicktempmail-messages-table {
        font-size: 13px;
    }
    
    .quicktempmail-messages-table th,
    .quicktempmail-messages-table td {
        padding: 8px;
    }
    
    .message-subject {
        max-width: 120px;
    }
    
    .modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px;
    }
    
    .content-body {
        max-height: 300px;
    }
}

@media (max-width: 480px) {
    .quicktempmail-messages-table th:nth-child(3),
    .quicktempmail-messages-table td:nth-child(3) {
        display: none;
    }
    
    .inbox-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .quicktempmail-btn {
        width: 100%;
    }
}

/* Theme Variations */
.quicktempmail-container.theme-dark {
    background: #2c3e50;
    border-color: #34495e;
    color: #ecf0f1;
}

.theme-dark .quicktempmail-email-section {
    background: #34495e;
    border-color: #4a5f7a;
}

.theme-dark .quicktempmail-header h3,
.theme-dark .quicktempmail-inbox-header h4 {
    color: #ecf0f1;
}

.theme-dark .quicktempmail-email-input {
    background: #2c3e50;
    border-color: #4a5f7a;
    color: #ecf0f1;
}

.theme-dark .quicktempmail-messages-table th {
    background: #34495e;
    color: #ecf0f1;
}

.theme-dark .quicktempmail-messages-table tbody tr:hover {
    background: #34495e;
}

/* Layout Variations */
.quicktempmail-container.layout-comfortable .quicktempmail-messages-table th,
.quicktempmail-container.layout-comfortable .quicktempmail-messages-table td {
    padding: 16px;
}

.quicktempmail-container.layout-spacious .quicktempmail-messages-table th,
.quicktempmail-container.layout-spacious .quicktempmail-messages-table td {
    padding: 20px;
}

.quicktempmail-container.layout-spacious .quicktempmail-email-section,
.quicktempmail-container.layout-spacious .quicktempmail-inbox-section {
    padding: 30px;
}

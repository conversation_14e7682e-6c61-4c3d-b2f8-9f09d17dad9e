<?php
/**
 * Shortcode class for QuickTempMail WP
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}


class QuickTempMail_Shortcode {
    /**
     * Constructor
     */
    public function __construct() {
        add_shortcode('quicktempmail', array($this, 'render_shortcode'));
    }
    /**
     * Render shortcode
     */
    public function render_shortcode($atts) {
        // Session already started in init action
        
        // Parse attributes
        $atts = shortcode_atts(array(
            'theme' => get_option('quicktempmail_color_scheme', 'default'),
            'auto_refresh' => get_option('quicktempmail_auto_refresh_interval', 15),
            'layout' => get_option('quicktempmail_table_layout', 'compact'),
            'show_timer' => 'true',
            'show_copy_button' => 'true',
            'show_refresh_button' => 'true',
            'height' => 'auto'
        ), $atts, 'quicktempmail');
        
        // Sanitize attributes
        $theme = sanitize_text_field($atts['theme']);
        $auto_refresh = intval($atts['auto_refresh']);
        $layout = sanitize_text_field($atts['layout']);
        $show_timer = filter_var($atts['show_timer'], FILTER_VALIDATE_BOOLEAN);
        $show_copy_button = filter_var($atts['show_copy_button'], FILTER_VALIDATE_BOOLEAN);
        $show_refresh_button = filter_var($atts['show_refresh_button'], FILTER_VALIDATE_BOOLEAN);
        $height = sanitize_text_field($atts['height']);
        
        // Generate unique ID for this instance
        $instance_id = 'quicktempmail-' . wp_rand(1000, 9999);
        
        // Check if user has an active email
        $database = new QuickTempMail_Database();
        $active_email = $database->get_active_email();
        
        $initial_email = '';
        $initial_expires = '';
        $initial_remaining = 0;
        
        if ($active_email && strtotime($active_email->expires_at) > time()) {
            $initial_email = $active_email->email_address;
            $initial_expires = $active_email->expires_at;
            $initial_remaining = strtotime($active_email->expires_at) - time();
        }
        
        // Build the HTML
        ob_start();
        ?>
        <style>
        /* QuickTempMail WP: Force visibility and layout for input and buttons */
        .quicktempmail-container {
            position: relative;
            z-index: 1000;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px 16px;
            max-width: 480px;
            margin: 24px auto;
            box-shadow: 0 2px 12px rgba(0,0,0,0.07);
        }
        .quicktempmail-email-input {
            display: block !important;
            width: 100% !important;
            max-width: 320px;
            font-size: 1.1em;
            padding: 8px 12px;
            border: 1px solid #bbb;
            border-radius: 4px 0 0 4px;
            background: #f9f9f9;
            color: #222;
            outline: none;
        }
        .email-input-group {
            display: flex !important;
            flex-direction: row;
            align-items: stretch;
            margin-bottom: 10px;
            gap: 0;
        }
        .email-buttons {
            display: flex !important;
            flex-direction: row;
            align-items: stretch;
            gap: 0;
        }
        .quicktempmail-btn {
            display: inline-flex !important;
            align-items: center;
            justify-content: center;
            padding: 0 14px;
            height: 38px;
            border: none;
            background: #0073aa;
            color: #fff;
            font-size: 1em;
            border-radius: 0 4px 4px 0;
            margin-left: 2px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .quicktempmail-btn:hover, .quicktempmail-btn:focus {
            background: #005177;
        }
        .quicktempmail-copy-btn {
            border-radius: 0;
            background: #4caf50;
            margin-left: 0;
        }
        .quicktempmail-copy-btn:hover, .quicktempmail-copy-btn:focus {
            background: #388e3c;
        }
        .quicktempmail-generate-btn {
            border-radius: 0 4px 4px 0;
            background: #0073aa;
        }
        .quicktempmail-generate-btn:hover, .quicktempmail-generate-btn:focus {
            background: #005177;
        }
        .quicktempmail-refresh-btn {
            border-radius: 4px;
            background: #ff9800;
            margin-left: 2px;
        }
        .quicktempmail-refresh-btn:hover, .quicktempmail-refresh-btn:focus {
            background: #e65100;
        }
        /* Ensure no theme hides the input/buttons */
        .quicktempmail-email-input, .quicktempmail-btn {
            visibility: visible !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        }
        </style>
        <div id="<?php echo esc_attr($instance_id); ?>" class="quicktempmail-container theme-<?php echo esc_attr($theme); ?> layout-<?php echo esc_attr($layout); ?>" 
             data-auto-refresh="<?php echo esc_attr($auto_refresh); ?>" 
             data-show-timer="<?php echo esc_attr($show_timer ? 'true' : 'false'); ?>"
             data-show-copy="<?php echo esc_attr($show_copy_button ? 'true' : 'false'); ?>"
             data-show-refresh="<?php echo esc_attr($show_refresh_button ? 'true' : 'false'); ?>"
             style="<?php echo $height !== 'auto' ? 'height: ' . esc_attr($height) . ';' : ''; ?>">
            <!-- Email Generation Section -->
            <div class="quicktempmail-email-section">
                <div class="quicktempmail-header">
                    <h3><?php _e('Temporary Email Address', 'quicktempmail-wp'); ?></h3>
                    <?php if ($show_timer): ?>
                    <div class="quicktempmail-timer" data-expires="<?php echo esc_attr($initial_expires); ?>">
                        <span class="timer-label"><?php _e('Expires in:', 'quicktempmail-wp'); ?></span>
                        <span class="timer-value"><?php echo $this->format_time_remaining($initial_remaining); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="quicktempmail-email-display">
                    <div class="email-input-group">
                        <input type="text" 
                               id="<?php echo esc_attr($instance_id); ?>-email" 
                               class="quicktempmail-email-input" 
                               value="<?php echo esc_attr($initial_email); ?>" 
                               readonly 
                               placeholder="<?php _e('Click Generate to create a temporary email', 'quicktempmail-wp'); ?>">
                        <div class="email-buttons">
                            <?php if ($show_copy_button): ?>
                            <button type="button" class="quicktempmail-btn quicktempmail-copy-btn" title="<?php _e('Copy to clipboard', 'quicktempmail-wp'); ?>">
                                <span class="btn-icon">📋</span>
                                <span class="btn-text"><?php _e('Copy', 'quicktempmail-wp'); ?></span>
                            </button>
                            <?php endif; ?>
                            <button type="button" class="quicktempmail-btn quicktempmail-generate-btn">
                                <span class="btn-icon">🔄</span>
                                <span class="btn-text"><?php _e('Generate', 'quicktempmail-wp'); ?></span>
                            </button>
                            <?php if ($show_refresh_button): ?>
                            <button type="button" class="quicktempmail-btn quicktempmail-refresh-btn" title="<?php _e('Refresh inbox', 'quicktempmail-wp'); ?>">
                                <span class="btn-icon">↻</span>
                                <span class="btn-text"><?php _e('Refresh', 'quicktempmail-wp'); ?></span>
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="quicktempmail-status"></div>
                </div>
            </div>
            <!-- Inbox Section -->
            <div class="quicktempmail-inbox-section">
                <div class="quicktempmail-inbox-header">
                    <h4><?php _e('Inbox', 'quicktempmail-wp'); ?></h4>
                    <div class="inbox-controls">
                        <span class="message-count">0 <?php _e('messages', 'quicktempmail-wp'); ?></span>
                        <div class="auto-refresh-indicator" style="display: none;">
                            <span class="refresh-dot"></span>
                            <?php _e('Auto-refreshing...', 'quicktempmail-wp'); ?>
                        </div>
                    </div>
                </div>
                <div class="quicktempmail-inbox-content">
                    <div class="inbox-loading" style="display: none;">
                        <div class="loading-spinner"></div>
                        <span><?php _e('Loading messages...', 'quicktempmail-wp'); ?></span>
                    </div>
                    <div class="inbox-empty">
                        <div class="empty-icon">📭</div>
                        <p><?php _e('No emails received yet.', 'quicktempmail-wp'); ?></p>
                        <p class="empty-subtitle"><?php _e('Your temporary email is ready to receive messages.', 'quicktempmail-wp'); ?></p>
                    </div>
                    <div class="inbox-messages">
                        <table class="quicktempmail-messages-table">
                            <thead>
                                <tr>
                                    <th><?php _e('From', 'quicktempmail-wp'); ?></th>
                                    <th><?php _e('Subject', 'quicktempmail-wp'); ?></th>
                                    <th><?php _e('Received', 'quicktempmail-wp'); ?></th>
                                    <th><?php _e('Actions', 'quicktempmail-wp'); ?></th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- Email Modal -->
        <div id="<?php echo esc_attr($instance_id); ?>-modal" class="quicktempmail-modal" style="display: none;">
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><?php _e('Email Content', 'quicktempmail-wp'); ?></h3>
                    <button type="button" class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="email-headers">
                        <div class="header-row">
                            <strong><?php _e('From:', 'quicktempmail-wp'); ?></strong>
                            <span class="email-from"></span>
                        </div>
                        <div class="header-row">
                            <strong><?php _e('To:', 'quicktempmail-wp'); ?></strong>
                            <span class="email-to"></span>
                        </div>
                        <div class="header-row">
                            <strong><?php _e('Subject:', 'quicktempmail-wp'); ?></strong>
                            <span class="email-subject"></span>
                        </div>
                        <div class="header-row">
                            <strong><?php _e('Date:', 'quicktempmail-wp'); ?></strong>
                            <span class="email-date"></span>
                        </div>
                    </div>
                    <div class="email-content">
                        <div class="content-tabs">
                            <button type="button" class="tab-btn active" data-tab="html"><?php _e('HTML', 'quicktempmail-wp'); ?></button>
                            <button type="button" class="tab-btn" data-tab="text"><?php _e('Text', 'quicktempmail-wp'); ?></button>
                        </div>
                        <div class="content-body">
                            <div class="content-html active"></div>
                            <div class="content-text"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="quicktempmail-btn quicktempmail-delete-btn">
                        <?php _e('Delete Email', 'quicktempmail-wp'); ?>
                    </button>
                    <button type="button" class="quicktempmail-btn modal-close-btn">
                        <?php _e('Close', 'quicktempmail-wp'); ?>
                    </button>
                </div>
            </div>
        </div>
        <script>
        jQuery(document).ready(function($) {
            // Initialize QuickTempMail for this instance
            if (typeof QuickTempMail !== 'undefined') {
                new QuickTempMail('<?php echo esc_js($instance_id); ?>');
            }
        });
        </script>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Format time remaining
     */
    private function format_time_remaining($seconds) {
        if ($seconds <= 0) {
            return __('Expired', 'quicktempmail-wp');
        }
        
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;
        
        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        } else {
            return sprintf('%02d:%02d', $minutes, $seconds);
        }
    }
}

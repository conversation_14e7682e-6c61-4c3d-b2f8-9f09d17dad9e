<?php
/**
 * AJA<PERSON> Handler class for QuickTempMail WP
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class QuickTempMail_Ajax_Handler {
    
    /**
     * API handler instance
     */
    private $api_handler;
    
    /**
     * Database instance
     */
    private $database;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->api_handler = new QuickTempMail_API_Handler();
        $this->database = new QuickTempMail_Database();
        
        $this->init_hooks();
    }
    
    /**
     * Initialize AJAX hooks
     */
    private function init_hooks() {
        // Public AJAX actions (for non-logged-in users)
        add_action('wp_ajax_nopriv_quicktempmail_generate_email', array($this, 'generate_email'));
        add_action('wp_ajax_nopriv_quicktempmail_get_inbox', array($this, 'get_inbox'));
        add_action('wp_ajax_nopriv_quicktempmail_get_message', array($this, 'get_message'));
        add_action('wp_ajax_nopriv_quicktempmail_delete_message', array($this, 'delete_message'));
        add_action('wp_ajax_nopriv_quicktempmail_refresh_email', array($this, 'refresh_email'));
        
        // Logged-in user AJAX actions
        add_action('wp_ajax_quicktempmail_generate_email', array($this, 'generate_email'));
        add_action('wp_ajax_quicktempmail_get_inbox', array($this, 'get_inbox'));
        add_action('wp_ajax_quicktempmail_get_message', array($this, 'get_message'));
        add_action('wp_ajax_quicktempmail_delete_message', array($this, 'delete_message'));
        add_action('wp_ajax_quicktempmail_refresh_email', array($this, 'refresh_email'));
        
        // Admin-only AJAX actions
        add_action('wp_ajax_quicktempmail_test_api', array($this, 'test_api'));
        add_action('wp_ajax_quicktempmail_cleanup_expired', array($this, 'cleanup_expired'));
        add_action('wp_ajax_quicktempmail_get_stats', array($this, 'get_stats'));
    }
    
    /**
     * Generate new temporary email
     */
    public function generate_email() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }
        
        // Check if user already has an active email
        $existing_email = $this->database->get_active_email();
        if ($existing_email && !empty($_POST['force_new'])) {
            $this->database->deactivate_email($existing_email->id);
        } elseif ($existing_email) {
            wp_send_json_success(array(
                'email' => $existing_email->email_address,
                'expires_at' => $existing_email->expires_at,
                'time_remaining' => strtotime($existing_email->expires_at) - time()
            ));
        }
        
        // Create new email account
        $result = $this->api_handler->create_account();
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        // Store in database
        $db_id = $this->database->store_email($result);
        
        if (is_wp_error($db_id)) {
            wp_send_json_error(array('message' => $db_id->get_error_message()));
        }
        
        $expires_at = date('Y-m-d H:i:s', time() + get_option('quicktempmail_default_expiry', 3600));
        
        wp_send_json_success(array(
            'email' => $result['address'],
            'expires_at' => $expires_at,
            'time_remaining' => get_option('quicktempmail_default_expiry', 3600),
            'message' => __('New temporary email generated successfully', 'quicktempmail-wp')
        ));
    }
    
    /**
     * Get inbox messages
     */
    public function get_inbox() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }
        
        // Get active email
        $email_data = $this->database->get_active_email();
        
        if (!$email_data) {
            wp_send_json_error(array('message' => __('No active email found', 'quicktempmail-wp')));
        }
        
        // Check if email has expired
        if (strtotime($email_data->expires_at) < time()) {
            $this->database->deactivate_email($email_data->id);
            wp_send_json_error(array('message' => __('Email has expired', 'quicktempmail-wp')));
        }
        
        // Get messages from API
        $messages = $this->api_handler->get_messages($email_data->token);
        
        if (is_wp_error($messages)) {
            wp_send_json_error(array('message' => $messages->get_error_message()));
        }
        
        // Update last checked time
        $this->database->update_last_checked($email_data->id);
        
        // Process and format messages
        $formatted_messages = array();
        $message_data = $messages['data'] ?? array();
        
        foreach ($message_data as $message) {
            $formatted_messages[] = array(
                'id' => $message['id'],
                'from' => $message['from']['address'] ?? '',
                'from_name' => $message['from']['name'] ?? '',
                'subject' => $message['subject'] ?? __('(No Subject)', 'quicktempmail-wp'),
                'received_at' => $message['createdAt'],
                'received_relative' => $this->time_ago($message['createdAt']),
                'is_read' => $message['seen'] ?? false
            );
            
            // Store message in database for caching
            $this->database->store_message($email_data->id, $message);
        }
        
        // Update message count
        $this->database->update_message_count($email_data->id, count($formatted_messages));
        
        wp_send_json_success(array(
            'messages' => $formatted_messages,
            'count' => count($formatted_messages),
            'email' => $email_data->email_address,
            'time_remaining' => strtotime($email_data->expires_at) - time()
        ));
    }
    
    /**
     * Get specific message content
     */
    public function get_message() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        $message_id = sanitize_text_field($_POST['message_id'] ?? '');
        
        if (empty($message_id)) {
            wp_send_json_error(array('message' => __('Message ID is required', 'quicktempmail-wp')));
        }
        
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }
        
        // Get active email
        $email_data = $this->database->get_active_email();
        
        if (!$email_data) {
            wp_send_json_error(array('message' => __('No active email found', 'quicktempmail-wp')));
        }
        
        // Get message from API
        $message = $this->api_handler->get_message($message_id, $email_data->token);
        
        if (is_wp_error($message)) {
            wp_send_json_error(array('message' => $message->get_error_message()));
        }
        
        // Format message content
        $formatted_message = array(
            'id' => $message['id'],
            'from' => $message['from']['address'] ?? '',
            'from_name' => $message['from']['name'] ?? '',
            'to' => $message['to'][0]['address'] ?? '',
            'subject' => $message['subject'] ?? __('(No Subject)', 'quicktempmail-wp'),
            'body_text' => $message['text'] ?? '',
            'body_html' => $message['html'] ?? '',
            'received_at' => $message['createdAt'],
            'received_formatted' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($message['createdAt'])),
            'attachments' => $message['attachments'] ?? array()
        );
        
        wp_send_json_success($formatted_message);
    }
    
    /**
     * Delete message
     */
    public function delete_message() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        $message_id = sanitize_text_field($_POST['message_id'] ?? '');
        
        if (empty($message_id)) {
            wp_send_json_error(array('message' => __('Message ID is required', 'quicktempmail-wp')));
        }
        
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }
        
        // Get active email
        $email_data = $this->database->get_active_email();
        
        if (!$email_data) {
            wp_send_json_error(array('message' => __('No active email found', 'quicktempmail-wp')));
        }
        
        // Delete from API
        $result = $this->api_handler->delete_message($message_id, $email_data->token);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        // Delete from database
        $this->database->delete_message($message_id, $email_data->id);
        
        wp_send_json_success(array('message' => __('Message deleted successfully', 'quicktempmail-wp')));
    }
    
    /**
     * Refresh email (extend expiry)
     */
    public function refresh_email() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }
        
        // Get active email
        $email_data = $this->database->get_active_email();
        
        if (!$email_data) {
            wp_send_json_error(array('message' => __('No active email found', 'quicktempmail-wp')));
        }
        
        // Update last checked time
        $this->database->update_last_checked($email_data->id);
        
        wp_send_json_success(array(
            'email' => $email_data->email_address,
            'time_remaining' => strtotime($email_data->expires_at) - time(),
            'message' => __('Email refreshed successfully', 'quicktempmail-wp')
        ));
    }
    
    /**
     * Test API connection (admin only)
     */
    public function test_api() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'quicktempmail-wp')));
        }
        
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_admin_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        $health_check = $this->api_handler->check_api_health();
        
        wp_send_json_success($health_check);
    }
    
    /**
     * Manual cleanup of expired emails (admin only)
     */
    public function cleanup_expired() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'quicktempmail-wp')));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_admin_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }

        $deleted_count = $this->database->cleanup_expired_emails();

        wp_send_json_success(array(
            'deleted_count' => $deleted_count,
            'message' => sprintf(__('Cleaned up %d expired emails', 'quicktempmail-wp'), $deleted_count)
        ));
    }

    /**
     * Get usage statistics (admin only)
     */
    public function get_stats() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'quicktempmail-wp')));
        }

        $stats = $this->database->get_usage_stats();

        wp_send_json_success($stats);
    }
    
    /**
     * Calculate time ago
     */
    private function time_ago($datetime) {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) {
            return __('Just now', 'quicktempmail-wp');
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return sprintf(_n('%d minute ago', '%d minutes ago', $minutes, 'quicktempmail-wp'), $minutes);
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return sprintf(_n('%d hour ago', '%d hours ago', $hours, 'quicktempmail-wp'), $hours);
        } else {
            $days = floor($time / 86400);
            return sprintf(_n('%d day ago', '%d days ago', $days, 'quicktempmail-wp'), $days);
        }
    }
}

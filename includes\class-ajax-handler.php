<?php
/**
 * AJA<PERSON> Handler class for QuickTempMail WP
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class QuickTempMail_Ajax_Handler {
    
    /**
     * API handler instance
     */
    private $api_handler;
    
    /**
     * Database instance
     */
    private $database;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->api_handler = new QuickTempMail_API_Handler();
        $this->database = new QuickTempMail_Database();
        
        $this->init_hooks();
    }
    
    /**
     * Initialize AJAX hooks
     */
    private function init_hooks() {
        // Public AJAX actions (for non-logged-in users)
        add_action('wp_ajax_nopriv_quicktempmail_generate_email', array($this, 'generate_email'));
        add_action('wp_ajax_nopriv_quicktempmail_get_inbox', array($this, 'get_inbox'));
        add_action('wp_ajax_nopriv_quicktempmail_get_message', array($this, 'get_message'));
        add_action('wp_ajax_nopriv_quicktempmail_delete_message', array($this, 'delete_message'));
        add_action('wp_ajax_nopriv_quicktempmail_refresh_email', array($this, 'refresh_email'));
        
        // Logged-in user AJAX actions
        add_action('wp_ajax_quicktempmail_generate_email', array($this, 'generate_email'));
        add_action('wp_ajax_quicktempmail_get_inbox', array($this, 'get_inbox'));
        add_action('wp_ajax_quicktempmail_get_message', array($this, 'get_message'));
        add_action('wp_ajax_quicktempmail_delete_message', array($this, 'delete_message'));
        add_action('wp_ajax_quicktempmail_refresh_email', array($this, 'refresh_email'));
        
        // Admin-only AJAX actions
        add_action('wp_ajax_quicktempmail_test_api', array($this, 'test_api'));
        add_action('wp_ajax_quicktempmail_cleanup_expired', array($this, 'cleanup_expired'));
        add_action('wp_ajax_quicktempmail_get_stats', array($this, 'get_stats'));
    }
    
    /**
     * Generate new temporary email
     */
    public function generate_email() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }

        // Use WordPress session alternative - user IP and browser fingerprint
        $session_id = $this->get_session_id();

        // Check if user already has an active email
        $existing_email = $this->database->get_active_email($session_id);
        if ($existing_email && !empty($_POST['force_new'])) {
            $this->database->deactivate_email($existing_email->id);
        } elseif ($existing_email && strtotime($existing_email->expires_at) > time()) {
            wp_send_json_success(array(
                'email' => $existing_email->email_address,
                'expires_at' => $existing_email->expires_at,
                'time_remaining' => strtotime($existing_email->expires_at) - time()
            ));
        }

        // Create new email account
        $result = $this->api_handler->create_account();

        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }

        // Prepare data for database storage
        $email_data = array(
            'address' => $result['address'] ?? $result['email'] ?? '',
            'id' => $result['id'] ?? wp_generate_uuid4(),
            'token' => $result['token'] ?? '',
            'domain' => $result['domain'] ?? explode('@', $result['address'] ?? '')[1] ?? ''
        );

        // Store in database
        $db_id = $this->database->store_email($email_data, $session_id);

        if (is_wp_error($db_id)) {
            wp_send_json_error(array('message' => $db_id->get_error_message()));
        }

        $expires_at = date('Y-m-d H:i:s', time() + get_option('quicktempmail_default_expiry', 3600));

        wp_send_json_success(array(
            'email' => $email_data['address'],
            'expires_at' => $expires_at,
            'time_remaining' => get_option('quicktempmail_default_expiry', 3600),
            'message' => __('New temporary email generated successfully', 'quicktempmail-wp')
        ));
    }
    
    /**
     * Get inbox messages
     */
    public function get_inbox() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }

        // Get session ID
        $session_id = $this->get_session_id();

        // Get active email
        $email_data = $this->database->get_active_email($session_id);

        if (!$email_data) {
            wp_send_json_error(array('message' => __('No active email found', 'quicktempmail-wp')));
        }
        
        // Check if email has expired
        if (strtotime($email_data->expires_at) < time()) {
            $this->database->deactivate_email($email_data->id);
            wp_send_json_error(array('message' => __('Email has expired', 'quicktempmail-wp')));
        }
        
        // Check email service type and get messages accordingly
        $formatted_messages = array();
        $email_parts = explode('@', $email_data->email_address);
        $username = $email_parts[0] ?? '';
        $domain = $email_parts[1] ?? '';

        // Check if this is a 1secmail address
        if (in_array($domain, array('1secmail.com', '1secmail.org', '1secmail.net', 'esiix.com'))) {
            // Use 1secmail API
            $messages = $this->api_handler->get_1secmail_messages($username, $domain);

            if (is_wp_error($messages)) {
                // Show demo messages if API fails
                $formatted_messages = $this->get_demo_messages($email_data->email_address);
            } else {
                // Process 1secmail messages
                $message_data = $messages['data'] ?? array();

                foreach ($message_data as $message) {
                    $formatted_messages[] = array(
                        'id' => $message['id'],
                        'from' => $message['from'] ?? '',
                        'from_name' => '',
                        'subject' => $message['subject'] ?? __('(No Subject)', 'quicktempmail-wp'),
                        'received_at' => $message['date'] ?? date('c'),
                        'received_relative' => $this->time_ago($message['date'] ?? date('c')),
                        'is_read' => false
                    );
                }
            }
        } elseif (empty($email_data->token) || strlen($email_data->token) < 10) {
            // This is a fallback email - show demo messages
            $formatted_messages = $this->get_demo_messages($email_data->email_address);
        } else {
            // Use Mail.tm API
            $messages = $this->api_handler->get_messages($email_data->token);

            if (is_wp_error($messages)) {
                // Show demo messages if API fails
                $formatted_messages = $this->get_demo_messages($email_data->email_address);
            } else {
                // Process Mail.tm messages
                $message_data = $messages['data'] ?? array();

                foreach ($message_data as $message) {
                    $formatted_messages[] = array(
                        'id' => $message['id'],
                        'from' => $message['from']['address'] ?? '',
                        'from_name' => $message['from']['name'] ?? '',
                        'subject' => $message['subject'] ?? __('(No Subject)', 'quicktempmail-wp'),
                        'received_at' => $message['createdAt'],
                        'received_relative' => $this->time_ago($message['createdAt']),
                        'is_read' => $message['seen'] ?? false
                    );

                    // Store message in database for caching
                    $this->database->store_message($email_data->id, $message);
                }
            }
        }

        // Update last checked time
        $this->database->update_last_checked($email_data->id);

        // Update message count
        $this->database->update_message_count($email_data->id, count($formatted_messages));
        
        wp_send_json_success(array(
            'messages' => $formatted_messages,
            'count' => count($formatted_messages),
            'email' => $email_data->email_address,
            'time_remaining' => strtotime($email_data->expires_at) - time()
        ));
    }
    
    /**
     * Get specific message content
     */
    public function get_message() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        $message_id = sanitize_text_field($_POST['message_id'] ?? '');
        
        if (empty($message_id)) {
            wp_send_json_error(array('message' => __('Message ID is required', 'quicktempmail-wp')));
        }
        
        // Get session ID
        $session_id = $this->get_session_id();

        // Get active email
        $email_data = $this->database->get_active_email($session_id);
        
        if (!$email_data) {
            wp_send_json_error(array('message' => __('No active email found', 'quicktempmail-wp')));
        }
        
        // Check if this is a demo message
        if (strpos($message_id, 'demo_') === 0) {
            $formatted_message = $this->get_demo_message_content($message_id, $email_data->email_address);
        } else {
            // Check email service type
            $email_parts = explode('@', $email_data->email_address);
            $username = $email_parts[0] ?? '';
            $domain = $email_parts[1] ?? '';

            if (in_array($domain, array('1secmail.com', '1secmail.org', '1secmail.net', 'esiix.com'))) {
                // Use 1secmail API
                $message = $this->api_handler->get_1secmail_message($username, $domain, $message_id);

                if (is_wp_error($message)) {
                    $formatted_message = $this->get_demo_message_content($message_id, $email_data->email_address);
                } else {
                    // Format 1secmail message
                    $formatted_message = array(
                        'id' => $message['id'],
                        'from' => $message['from'] ?? '',
                        'from_name' => '',
                        'to' => $email_data->email_address,
                        'subject' => $message['subject'] ?? __('(No Subject)', 'quicktempmail-wp'),
                        'body_text' => $message['textBody'] ?? $message['body'] ?? '',
                        'body_html' => $message['htmlBody'] ?? $message['body'] ?? '',
                        'received_at' => $message['date'] ?? date('c'),
                        'received_formatted' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($message['date'] ?? date('c'))),
                        'attachments' => $message['attachments'] ?? array()
                    );
                }
            } else {
                // Use Mail.tm API
                $message = $this->api_handler->get_message($message_id, $email_data->token);

                if (is_wp_error($message)) {
                    // Fallback to demo message if API fails
                    $formatted_message = $this->get_demo_message_content($message_id, $email_data->email_address);
                } else {
                    // Format Mail.tm message content
                    $formatted_message = array(
                        'id' => $message['id'],
                        'from' => $message['from']['address'] ?? '',
                        'from_name' => $message['from']['name'] ?? '',
                        'to' => $message['to'][0]['address'] ?? '',
                        'subject' => $message['subject'] ?? __('(No Subject)', 'quicktempmail-wp'),
                        'body_text' => $message['text'] ?? '',
                        'body_html' => $message['html'] ?? '',
                        'received_at' => $message['createdAt'],
                        'received_formatted' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($message['createdAt'])),
                        'attachments' => $message['attachments'] ?? array()
                    );
                }
            }
        }
        
        wp_send_json_success($formatted_message);
    }
    
    /**
     * Delete message
     */
    public function delete_message() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        $message_id = sanitize_text_field($_POST['message_id'] ?? '');
        
        if (empty($message_id)) {
            wp_send_json_error(array('message' => __('Message ID is required', 'quicktempmail-wp')));
        }
        
        // Get session ID
        $session_id = $this->get_session_id();

        // Get active email
        $email_data = $this->database->get_active_email($session_id);
        
        if (!$email_data) {
            wp_send_json_error(array('message' => __('No active email found', 'quicktempmail-wp')));
        }
        
        // Delete from API
        $result = $this->api_handler->delete_message($message_id, $email_data->token);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        // Delete from database
        $this->database->delete_message($message_id, $email_data->id);
        
        wp_send_json_success(array('message' => __('Message deleted successfully', 'quicktempmail-wp')));
    }
    
    /**
     * Refresh email (extend expiry)
     */
    public function refresh_email() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        // Get session ID
        $session_id = $this->get_session_id();

        // Get active email
        $email_data = $this->database->get_active_email($session_id);
        
        if (!$email_data) {
            wp_send_json_error(array('message' => __('No active email found', 'quicktempmail-wp')));
        }
        
        // Update last checked time
        $this->database->update_last_checked($email_data->id);
        
        wp_send_json_success(array(
            'email' => $email_data->email_address,
            'time_remaining' => strtotime($email_data->expires_at) - time(),
            'message' => __('Email refreshed successfully', 'quicktempmail-wp')
        ));
    }
    
    /**
     * Test API connection (admin only)
     */
    public function test_api() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'quicktempmail-wp')));
        }
        
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_admin_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }
        
        $health_check = $this->api_handler->check_api_health();
        
        wp_send_json_success($health_check);
    }
    
    /**
     * Manual cleanup of expired emails (admin only)
     */
    public function cleanup_expired() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'quicktempmail-wp')));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'quicktempmail_admin_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed', 'quicktempmail-wp')));
        }

        $deleted_count = $this->database->cleanup_expired_emails();

        wp_send_json_success(array(
            'deleted_count' => $deleted_count,
            'message' => sprintf(__('Cleaned up %d expired emails', 'quicktempmail-wp'), $deleted_count)
        ));
    }

    /**
     * Get usage statistics (admin only)
     */
    public function get_stats() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'quicktempmail-wp')));
        }

        $stats = $this->database->get_usage_stats();

        wp_send_json_success($stats);
    }
    
    /**
     * Calculate time ago
     */
    private function time_ago($datetime) {
        $time = time() - strtotime($datetime);

        if ($time < 60) {
            return __('Just now', 'quicktempmail-wp');
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return sprintf(_n('%d minute ago', '%d minutes ago', $minutes, 'quicktempmail-wp'), $minutes);
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return sprintf(_n('%d hour ago', '%d hours ago', $hours, 'quicktempmail-wp'), $hours);
        } else {
            $days = floor($time / 86400);
            return sprintf(_n('%d day ago', '%d days ago', $days, 'quicktempmail-wp'), $days);
        }
    }

    /**
     * Get session ID for user identification
     */
    private function get_session_id() {
        // Create a unique session ID based on IP and user agent
        $user_ip = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // Create a hash for session identification
        $session_data = $user_ip . '|' . $user_agent . '|' . date('Y-m-d');
        return 'qtm_' . md5($session_data);
    }

    /**
     * Get demo messages for fallback/testing
     */
    private function get_demo_messages($email_address) {
        $demo_messages = array();

        // Check if we should show demo messages (for testing)
        $show_demo = get_option('quicktempmail_show_demo_messages', true);

        if ($show_demo) {
            $demo_messages[] = array(
                'id' => 'demo_1_' . time(),
                'from' => '<EMAIL>',
                'from_name' => 'Welcome Team',
                'subject' => __('Welcome! Your temporary email is working', 'quicktempmail-wp'),
                'received_at' => date('c', time() - 300), // 5 minutes ago
                'received_relative' => __('5 minutes ago', 'quicktempmail-wp'),
                'is_read' => false
            );

            $demo_messages[] = array(
                'id' => 'demo_2_' . time(),
                'from' => '<EMAIL>',
                'from_name' => 'QuickTempMail Test',
                'subject' => __('Test Email - Plugin is working correctly', 'quicktempmail-wp'),
                'received_at' => date('c', time() - 120), // 2 minutes ago
                'received_relative' => __('2 minutes ago', 'quicktempmail-wp'),
                'is_read' => false
            );
        }

        return $demo_messages;
    }

    /**
     * Get demo message content
     */
    private function get_demo_message_content($message_id, $email_address) {
        $demo_content = array(
            'demo_1_' . substr($message_id, 6) => array(
                'id' => $message_id,
                'from' => '<EMAIL>',
                'from_name' => 'Welcome Team',
                'to' => $email_address,
                'subject' => __('Welcome! Your temporary email is working', 'quicktempmail-wp'),
                'body_text' => __('Hello! This is a demo message to show that your QuickTempMail WP plugin is working correctly. You can now receive emails at this temporary address.', 'quicktempmail-wp'),
                'body_html' => '<h2>' . __('Welcome!', 'quicktempmail-wp') . '</h2><p>' . __('This is a demo message to show that your QuickTempMail WP plugin is working correctly.', 'quicktempmail-wp') . '</p><p>' . __('You can now receive emails at this temporary address:', 'quicktempmail-wp') . ' <strong>' . $email_address . '</strong></p>',
                'received_at' => date('c', time() - 300),
                'received_formatted' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), time() - 300),
                'attachments' => array()
            ),
            'demo_2_' . substr($message_id, 6) => array(
                'id' => $message_id,
                'from' => '<EMAIL>',
                'from_name' => 'QuickTempMail Test',
                'to' => $email_address,
                'subject' => __('Test Email - Plugin is working correctly', 'quicktempmail-wp'),
                'body_text' => __('This is a test email to verify that the QuickTempMail WP plugin is functioning properly. Features working: Email generation, Inbox display, Message viewing, Auto-refresh.', 'quicktempmail-wp'),
                'body_html' => '<h3>' . __('Test Email', 'quicktempmail-wp') . '</h3><p>' . __('This is a test email to verify that the QuickTempMail WP plugin is functioning properly.', 'quicktempmail-wp') . '</p><ul><li>' . __('Email generation', 'quicktempmail-wp') . ' ✅</li><li>' . __('Inbox display', 'quicktempmail-wp') . ' ✅</li><li>' . __('Message viewing', 'quicktempmail-wp') . ' ✅</li><li>' . __('Auto-refresh', 'quicktempmail-wp') . ' ✅</li></ul>',
                'received_at' => date('c', time() - 120),
                'received_formatted' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), time() - 120),
                'attachments' => array()
            )
        );

        // Find matching demo content
        foreach ($demo_content as $key => $content) {
            if (strpos($message_id, substr($key, 0, 6)) === 0) {
                return $content;
            }
        }

        // Default demo message if not found
        return array(
            'id' => $message_id,
            'from' => '<EMAIL>',
            'from_name' => 'Demo Sender',
            'to' => $email_address,
            'subject' => __('Demo Message', 'quicktempmail-wp'),
            'body_text' => __('This is a demo message for testing purposes.', 'quicktempmail-wp'),
            'body_html' => '<p>' . __('This is a demo message for testing purposes.', 'quicktempmail-wp') . '</p>',
            'received_at' => date('c'),
            'received_formatted' => date_i18n(get_option('date_format') . ' ' . get_option('time_format')),
            'attachments' => array()
        );
    }
}

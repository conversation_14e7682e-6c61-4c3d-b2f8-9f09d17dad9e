/**
 * QuickTempMail WP Frontend JavaScript
 */

class QuickTempMail {
    constructor(instanceId) {
        this.instanceId = instanceId;
        this.container = document.getElementById(instanceId);
        this.autoRefreshInterval = null;
        this.timerInterval = null;
        this.currentEmail = '';
        this.currentToken = '';
        
        if (!this.container) {
            console.error('QuickTempMail: Container not found');
            return;
        }
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.startTimer();
        
        // Auto-generate email if none exists
        const emailInput = this.container.querySelector('.quicktempmail-email-input');
        if (!emailInput.value) {
            this.generateEmail();
        } else {
            this.currentEmail = emailInput.value;
            this.startAutoRefresh();
            this.refreshInbox();
        }
    }
    
    bindEvents() {
        // Generate email button
        const generateBtn = this.container.querySelector('.quicktempmail-generate-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateEmail(true));
        }
        
        // Copy button
        const copyBtn = this.container.querySelector('.quicktempmail-copy-btn');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => this.copyEmail());
        }
        
        // Refresh button
        const refreshBtn = this.container.querySelector('.quicktempmail-refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshInbox());
        }
        
        // Message table clicks
        const messagesTable = this.container.querySelector('.quicktempmail-messages-table tbody');
        if (messagesTable) {
            messagesTable.addEventListener('click', (e) => {
                const row = e.target.closest('tr');
                if (row && row.dataset.messageId) {
                    this.viewMessage(row.dataset.messageId);
                }
            });
        }
        
        // Modal events
        const modal = document.getElementById(this.instanceId + '-modal');
        if (modal) {
            // Close modal
            modal.querySelectorAll('.modal-close, .modal-close-btn').forEach(btn => {
                btn.addEventListener('click', () => this.closeModal());
            });
            
            // Close on overlay click
            modal.querySelector('.modal-overlay').addEventListener('click', () => this.closeModal());
            
            // Tab switching
            modal.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
            });
            
            // Delete button
            const deleteBtn = modal.querySelector('.quicktempmail-delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => this.deleteCurrentMessage());
            }
        }
    }
    
    generateEmail(forceNew = false) {
        const generateBtn = this.container.querySelector('.quicktempmail-generate-btn');
        const emailInput = this.container.querySelector('.quicktempmail-email-input');
        
        this.setLoading(generateBtn, true);
        this.showStatus('info', quicktempmail_ajax.strings.loading);
        
        const data = {
            action: 'quicktempmail_generate_email',
            nonce: quicktempmail_ajax.nonce,
            force_new: forceNew ? 1 : 0
        };
        
        this.makeRequest(data)
            .then(response => {
                if (response.success) {
                    this.currentEmail = response.data.email;
                    emailInput.value = response.data.email;
                    this.updateTimer(response.data.time_remaining);
                    this.showStatus('success', response.data.message || quicktempmail_ajax.strings.copied);
                    this.startAutoRefresh();
                    this.refreshInbox();
                } else {
                    this.showStatus('error', response.data.message || quicktempmail_ajax.strings.error);
                }
            })
            .catch(error => {
                console.error('Generate email error:', error);
                this.showStatus('error', quicktempmail_ajax.strings.error);
            })
            .finally(() => {
                this.setLoading(generateBtn, false);
            });
    }
    
    copyEmail() {
        const emailInput = this.container.querySelector('.quicktempmail-email-input');
        const copyBtn = this.container.querySelector('.quicktempmail-copy-btn');
        
        if (!emailInput.value) {
            this.showStatus('error', 'No email to copy');
            return;
        }
        
        // Try modern clipboard API first
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(emailInput.value)
                .then(() => {
                    this.showCopySuccess(copyBtn);
                })
                .catch(() => {
                    this.fallbackCopy(emailInput);
                });
        } else {
            this.fallbackCopy(emailInput);
        }
    }
    
    fallbackCopy(emailInput) {
        const copyBtn = this.container.querySelector('.quicktempmail-copy-btn');
        
        try {
            emailInput.select();
            emailInput.setSelectionRange(0, 99999); // For mobile
            document.execCommand('copy');
            this.showCopySuccess(copyBtn);
        } catch (err) {
            console.error('Copy failed:', err);
            this.showStatus('error', quicktempmail_ajax.strings.copy_failed);
        }
    }
    
    showCopySuccess(copyBtn) {
        const originalText = copyBtn.querySelector('.btn-text').textContent;
        const originalClass = copyBtn.className;
        
        copyBtn.classList.add('copied');
        copyBtn.querySelector('.btn-text').textContent = 'Copied!';
        
        setTimeout(() => {
            copyBtn.className = originalClass;
            copyBtn.querySelector('.btn-text').textContent = originalText;
        }, 2000);
        
        this.showStatus('success', quicktempmail_ajax.strings.copied);
    }
    
    refreshInbox() {
        if (!this.currentEmail) {
            return;
        }
        
        const refreshBtn = this.container.querySelector('.quicktempmail-refresh-btn');
        const loadingDiv = this.container.querySelector('.inbox-loading');
        
        if (refreshBtn) this.setLoading(refreshBtn, true);
        if (loadingDiv) loadingDiv.style.display = 'flex';
        
        const data = {
            action: 'quicktempmail_get_inbox',
            nonce: quicktempmail_ajax.nonce
        };
        
        this.makeRequest(data)
            .then(response => {
                if (response.success) {
                    this.updateInbox(response.data.messages);
                    this.updateMessageCount(response.data.count);
                    this.updateTimer(response.data.time_remaining);
                } else {
                    this.showStatus('error', response.data.message || quicktempmail_ajax.strings.error);
                }
            })
            .catch(error => {
                console.error('Refresh inbox error:', error);
                this.showStatus('error', quicktempmail_ajax.strings.error);
            })
            .finally(() => {
                if (refreshBtn) this.setLoading(refreshBtn, false);
                if (loadingDiv) loadingDiv.style.display = 'none';
            });
    }
    
    updateInbox(messages) {
        const emptyDiv = this.container.querySelector('.inbox-empty');
        const messagesDiv = this.container.querySelector('.inbox-messages');
        const tbody = this.container.querySelector('.quicktempmail-messages-table tbody');
        
        if (messages.length === 0) {
            emptyDiv.style.display = 'block';
            messagesDiv.style.display = 'none';
            return;
        }
        
        emptyDiv.style.display = 'none';
        messagesDiv.style.display = 'block';
        
        tbody.innerHTML = '';
        
        messages.forEach(message => {
            const row = document.createElement('tr');
            row.dataset.messageId = message.id;
            
            row.innerHTML = `
                <td class="message-from">${this.escapeHtml(message.from)}</td>
                <td class="message-subject">${this.escapeHtml(message.subject)}</td>
                <td class="message-time">${this.escapeHtml(message.received_relative)}</td>
                <td class="message-actions">
                    <button class="action-btn view" onclick="event.stopPropagation();" data-message-id="${message.id}">View</button>
                    <button class="action-btn delete" onclick="event.stopPropagation();" data-message-id="${message.id}">Delete</button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
        
        // Bind action buttons
        tbody.querySelectorAll('.action-btn.view').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.viewMessage(btn.dataset.messageId);
            });
        });
        
        tbody.querySelectorAll('.action-btn.delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteMessage(btn.dataset.messageId);
            });
        });
    }
    
    viewMessage(messageId) {
        const modal = document.getElementById(this.instanceId + '-modal');
        
        this.showModal();
        
        const data = {
            action: 'quicktempmail_get_message',
            nonce: quicktempmail_ajax.nonce,
            message_id: messageId
        };
        
        this.makeRequest(data)
            .then(response => {
                if (response.success) {
                    this.populateModal(response.data);
                    modal.dataset.currentMessageId = messageId;
                } else {
                    this.showStatus('error', response.data.message || quicktempmail_ajax.strings.error);
                    this.closeModal();
                }
            })
            .catch(error => {
                console.error('View message error:', error);
                this.showStatus('error', quicktempmail_ajax.strings.error);
                this.closeModal();
            });
    }
    
    populateModal(messageData) {
        const modal = document.getElementById(this.instanceId + '-modal');
        
        modal.querySelector('.email-from').textContent = messageData.from;
        modal.querySelector('.email-to').textContent = messageData.to;
        modal.querySelector('.email-subject').textContent = messageData.subject;
        modal.querySelector('.email-date').textContent = messageData.received_formatted;
        
        modal.querySelector('.content-html').innerHTML = messageData.body_html || '<p>No HTML content</p>';
        modal.querySelector('.content-text').textContent = messageData.body_text || 'No text content';
    }
    
    deleteMessage(messageId) {
        if (!confirm(quicktempmail_ajax.strings.confirm_delete)) {
            return;
        }
        
        const data = {
            action: 'quicktempmail_delete_message',
            nonce: quicktempmail_ajax.nonce,
            message_id: messageId
        };
        
        this.makeRequest(data)
            .then(response => {
                if (response.success) {
                    this.showStatus('success', response.data.message);
                    this.refreshInbox();
                } else {
                    this.showStatus('error', response.data.message || quicktempmail_ajax.strings.error);
                }
            })
            .catch(error => {
                console.error('Delete message error:', error);
                this.showStatus('error', quicktempmail_ajax.strings.error);
            });
    }
    
    deleteCurrentMessage() {
        const modal = document.getElementById(this.instanceId + '-modal');
        const messageId = modal.dataset.currentMessageId;
        
        if (messageId) {
            this.deleteMessage(messageId);
            this.closeModal();
        }
    }
    
    showModal() {
        const modal = document.getElementById(this.instanceId + '-modal');
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
    
    closeModal() {
        const modal = document.getElementById(this.instanceId + '-modal');
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
    
    switchTab(tabName) {
        const modal = document.getElementById(this.instanceId + '-modal');
        
        // Update tab buttons
        modal.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });
        
        // Update content
        modal.querySelectorAll('.content-html, .content-text').forEach(content => {
            content.classList.toggle('active', content.classList.contains('content-' + tabName));
        });
    }
    
    startAutoRefresh() {
        const autoRefreshInterval = parseInt(this.container.dataset.autoRefresh) || 15;
        
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
        }
        
        this.autoRefreshInterval = setInterval(() => {
            this.refreshInbox();
            this.showAutoRefreshIndicator();
        }, autoRefreshInterval * 1000);
    }
    
    showAutoRefreshIndicator() {
        const indicator = this.container.querySelector('.auto-refresh-indicator');
        if (indicator) {
            indicator.style.display = 'flex';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 2000);
        }
    }
    
    startTimer() {
        const timerElement = this.container.querySelector('.timer-value');
        const expiresAt = this.container.querySelector('.quicktempmail-timer').dataset.expires;
        
        if (!timerElement || !expiresAt) {
            return;
        }
        
        this.timerInterval = setInterval(() => {
            const now = new Date().getTime();
            const expires = new Date(expiresAt).getTime();
            const remaining = Math.max(0, expires - now);
            
            this.updateTimerDisplay(timerElement, remaining);
            
            if (remaining <= 0) {
                clearInterval(this.timerInterval);
                this.showStatus('error', 'Email has expired');
                if (this.autoRefreshInterval) {
                    clearInterval(this.autoRefreshInterval);
                }
            }
        }, 1000);
    }
    
    updateTimer(seconds) {
        const timerElement = this.container.querySelector('.timer-value');
        if (timerElement) {
            this.updateTimerDisplay(timerElement, seconds * 1000);
        }
    }
    
    updateTimerDisplay(element, milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        let display;
        if (hours > 0) {
            display = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            display = `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        
        element.textContent = display;
        
        // Update color based on time remaining
        element.className = 'timer-value';
        if (seconds < 300) { // Less than 5 minutes
            element.classList.add('danger');
        } else if (seconds < 900) { // Less than 15 minutes
            element.classList.add('warning');
        }
    }
    
    updateMessageCount(count) {
        const countElement = this.container.querySelector('.message-count');
        if (countElement) {
            countElement.textContent = `${count} ${count === 1 ? 'message' : 'messages'}`;
        }
    }
    
    showStatus(type, message) {
        const statusDiv = this.container.querySelector('.quicktempmail-status');
        if (statusDiv) {
            statusDiv.className = `quicktempmail-status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }
    }
    
    setLoading(button, loading) {
        if (!button) return;
        
        if (loading) {
            button.disabled = true;
            button.dataset.originalText = button.querySelector('.btn-text').textContent;
            button.querySelector('.btn-text').textContent = quicktempmail_ajax.strings.loading;
        } else {
            button.disabled = false;
            if (button.dataset.originalText) {
                button.querySelector('.btn-text').textContent = button.dataset.originalText;
            }
        }
    }
    
    makeRequest(data) {
        return fetch(quicktempmail_ajax.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(data)
        }).then(response => response.json());
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    destroy() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
        }
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any existing QuickTempMail containers
    document.querySelectorAll('.quicktempmail-container').forEach(container => {
        if (!container.dataset.initialized) {
            new QuickTempMail(container.id);
            container.dataset.initialized = 'true';
        }
    });
});

// Make QuickTempMail available globally
window.QuickTempMail = QuickTempMail;

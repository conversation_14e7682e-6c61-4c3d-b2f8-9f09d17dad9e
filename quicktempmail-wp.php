<?php
/**
 * Plugin Name: QuickTempMail WP
 * Plugin URI: https://github.com/your-username/quicktempmail-wp
 * Description: A WordPress plugin that provides temporary email functionality using Mail.tm API, replicating temp-mail.org features.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: quicktempmail-wp
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}


// Define plugin constants
define('QUICKTEMPMAIL_VERSION', '1.0.0');
define('QUICKTEMPMAIL_PLUGIN_FILE', __FILE__);
define('QUICKTEMPMAIL_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('QUICKTEMPMAIL_PLUGIN_URL', plugin_dir_url(__FILE__));
define('QUICKTEMPMAIL_PLUGIN_BASENAME', plugin_basename(__FILE__));


// Always require the database class early so it's available for activation and cleanup
require_once QUICKTEMPMAIL_PLUGIN_DIR . 'includes/class-database.php';

// Ensure session is started before any output (only once, at init)
add_action('init', function() {
    if (!session_id()) {
        session_start();
    }
});

/**
 * Main QuickTempMail WP Class
 */
class QuickTempMail_WP {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Initialize plugin
        add_action('plugins_loaded', array($this, 'init'));
        
        // Load text domain
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Schedule cleanup cron job
        if (!wp_next_scheduled('quicktempmail_cleanup_expired')) {
            wp_schedule_event(time(), 'hourly', 'quicktempmail_cleanup_expired');
        }
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled cron job
        wp_clear_scheduled_hook('quicktempmail_cleanup_expired');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load required files
        $this->load_dependencies();
        
        // Initialize components
        $this->init_components();
    }
    
    /**
     * Load text domain for internationalization
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'quicktempmail-wp',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }
    
    /**
     * Load required files
     */
    private function load_dependencies() {
        // Core classes
        require_once QUICKTEMPMAIL_PLUGIN_DIR . 'includes/class-security.php';
        require_once QUICKTEMPMAIL_PLUGIN_DIR . 'includes/class-database.php';
        require_once QUICKTEMPMAIL_PLUGIN_DIR . 'includes/class-api-handler.php';
        require_once QUICKTEMPMAIL_PLUGIN_DIR . 'includes/class-ajax-handler.php';
        require_once QUICKTEMPMAIL_PLUGIN_DIR . 'includes/class-admin.php';
        require_once QUICKTEMPMAIL_PLUGIN_DIR . 'includes/class-shortcode.php';
    }
    
    /**
     * Initialize components
     */
    private function init_components() {
        // Initialize admin
        if (is_admin()) {
            new QuickTempMail_Admin();
        }
        
        // Initialize AJAX handler
        new QuickTempMail_Ajax_Handler();
        
        // Initialize shortcode
        new QuickTempMail_Shortcode();
        
        // Schedule cleanup
        add_action('quicktempmail_cleanup_expired', array($this, 'cleanup_expired_emails'));
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        wp_enqueue_style(
            'quicktempmail-frontend',
            QUICKTEMPMAIL_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            QUICKTEMPMAIL_VERSION
        );
        
        wp_enqueue_script(
            'quicktempmail-frontend',
            QUICKTEMPMAIL_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            QUICKTEMPMAIL_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('quicktempmail-frontend', 'quicktempmail_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('quicktempmail_nonce'),
            'strings' => array(
                'copied' => __('Copied to clipboard!', 'quicktempmail-wp'),
                'copy_failed' => __('Failed to copy to clipboard', 'quicktempmail-wp'),
                'loading' => __('Loading...', 'quicktempmail-wp'),
                'error' => __('An error occurred. Please try again.', 'quicktempmail-wp'),
                'no_emails' => __('No emails received yet.', 'quicktempmail-wp'),
                'confirm_delete' => __('Are you sure you want to delete this email?', 'quicktempmail-wp'),
            )
        ));
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on our admin pages
        if (strpos($hook, 'quicktempmail') === false) {
            return;
        }
        
        wp_enqueue_style(
            'quicktempmail-admin',
            QUICKTEMPMAIL_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            QUICKTEMPMAIL_VERSION
        );
        
        wp_enqueue_script(
            'quicktempmail-admin',
            QUICKTEMPMAIL_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            QUICKTEMPMAIL_VERSION,
            true
        );
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        $database = new QuickTempMail_Database();
        $database->create_tables();
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        $default_options = array(
            'api_base_url' => 'https://api.mail.tm',
            'api_timeout' => 30,
            'api_retry_attempts' => 3,
            'default_expiry' => 3600, // 1 hour
            'auto_refresh_interval' => 15, // 15 seconds
            'enable_custom_email' => true,
            'enable_auto_refresh' => true,
            'enable_email_deletion' => true,
            'color_scheme' => 'default',
            'table_layout' => 'compact'
        );
        
        foreach ($default_options as $key => $value) {
            if (get_option('quicktempmail_' . $key) === false) {
                add_option('quicktempmail_' . $key, $value);
            }
        }
    }
    
    /**
     * Cleanup expired emails
     */
    public function cleanup_expired_emails() {
        $database = new QuickTempMail_Database();
        $database->cleanup_expired_emails();
    }
}

// Initialize the plugin
function quicktempmail_wp() {
    return QuickTempMail_WP::get_instance();
}

// Start the plugin
quicktempmail_wp();

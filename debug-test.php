<?php
/**
 * Debug test page for QuickTempMail WP
 * This file helps debug API issues
 */

// WordPress environment
require_once('../../../wp-config.php');

// Include our classes
require_once('includes/class-api-handler.php');
require_once('includes/class-database.php');

echo "<h1>QuickTempMail WP Debug Test</h1>";

// Test 1: API Connection
echo "<h2>1. API Connection Test</h2>";
$api_handler = new QuickTempMail_API_Handler();
$health_check = $api_handler->check_api_health();

if ($health_check['status'] === 'ok') {
    echo "<p style='color: green;'>✅ API Connection: SUCCESS</p>";
    echo "<p>Domains available: " . ($health_check['domains_count'] ?? 'Unknown') . "</p>";
} else {
    echo "<p style='color: red;'>❌ API Connection: FAILED</p>";
    echo "<p>Error: " . $health_check['message'] . "</p>";
}

// Test 2: Get Domains
echo "<h2>2. Get Available Domains</h2>";
$domains = $api_handler->get_domains();

if (is_wp_error($domains)) {
    echo "<p style='color: red;'>❌ Get Domains: FAILED</p>";
    echo "<p>Error: " . $domains->get_error_message() . "</p>";
} else {
    echo "<p style='color: green;'>✅ Get Domains: SUCCESS</p>";
    echo "<p>Found " . count($domains) . " domains:</p>";
    echo "<ul>";
    foreach ($domains as $domain) {
        echo "<li>" . $domain['domain'] . " (ID: " . $domain['id'] . ")</li>";
    }
    echo "</ul>";
}

// Test 3: Create Account
echo "<h2>3. Create Test Account</h2>";
if (!is_wp_error($domains) && !empty($domains)) {
    $result = $api_handler->create_account();
    
    if (is_wp_error($result)) {
        echo "<p style='color: red;'>❌ Create Account: FAILED</p>";
        echo "<p>Error: " . $result->get_error_message() . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Create Account: SUCCESS</p>";
        echo "<p>Email: " . $result['address'] . "</p>";
        echo "<p>Token: " . substr($result['token'], 0, 20) . "...</p>";
        
        // Test 4: Database Storage
        echo "<h2>4. Database Storage Test</h2>";
        $database = new QuickTempMail_Database();
        
        // Create tables if they don't exist
        $database->create_tables();
        
        $db_result = $database->store_email($result, 'test_session_' . time());
        
        if (is_wp_error($db_result)) {
            echo "<p style='color: red;'>❌ Database Storage: FAILED</p>";
            echo "<p>Error: " . $db_result->get_error_message() . "</p>";
        } else {
            echo "<p style='color: green;'>✅ Database Storage: SUCCESS</p>";
            echo "<p>Stored with ID: " . $db_result . "</p>";
        }
    }
} else {
    echo "<p style='color: orange;'>⚠️ Skipping account creation due to domain fetch failure</p>";
}

// Test 5: WordPress AJAX URL
echo "<h2>5. WordPress AJAX Configuration</h2>";
echo "<p>AJAX URL: " . admin_url('admin-ajax.php') . "</p>";
echo "<p>WordPress Version: " . get_bloginfo('version') . "</p>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";

// Test 6: Plugin Options
echo "<h2>6. Plugin Options</h2>";
$options = array(
    'quicktempmail_api_base_url',
    'quicktempmail_api_timeout',
    'quicktempmail_api_retry_attempts',
    'quicktempmail_default_expiry'
);

echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Option</th><th>Value</th></tr>";
foreach ($options as $option) {
    $value = get_option($option, 'Not set');
    echo "<tr><td>{$option}</td><td>{$value}</td></tr>";
}
echo "</table>";

// Test 7: JavaScript Test
echo "<h2>7. JavaScript Test</h2>";
echo "<div id='js-test-result'>JavaScript not loaded</div>";
echo "<button onclick='testAjax()'>Test AJAX Call</button>";

?>

<script>
// Test if JavaScript is working
document.getElementById('js-test-result').innerHTML = '✅ JavaScript is working';

// Test AJAX call
function testAjax() {
    const resultDiv = document.getElementById('js-test-result');
    resultDiv.innerHTML = 'Testing AJAX...';
    
    const data = new FormData();
    data.append('action', 'quicktempmail_generate_email');
    data.append('nonce', '<?php echo wp_create_nonce('quicktempmail_nonce'); ?>');
    
    fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
        method: 'POST',
        body: data
    })
    .then(response => response.json())
    .then(data => {
        console.log('AJAX Response:', data);
        if (data.success) {
            resultDiv.innerHTML = '✅ AJAX Success: ' + data.data.email;
        } else {
            resultDiv.innerHTML = '❌ AJAX Failed: ' + (data.data.message || 'Unknown error');
        }
    })
    .catch(error => {
        console.error('AJAX Error:', error);
        resultDiv.innerHTML = '❌ AJAX Error: ' + error.message;
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
button { padding: 10px 20px; font-size: 16px; margin: 10px 0; }
</style>

<?php
echo "<hr>";
echo "<p><strong>Debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Instructions:</strong></p>";
echo "<ol>";
echo "<li>Check all tests above for any failures</li>";
echo "<li>Click 'Test AJAX Call' button to test frontend functionality</li>";
echo "<li>Open browser console (F12) to see detailed logs</li>";
echo "<li>If API connection fails, check your server's internet connection</li>";
echo "<li>If database fails, check WordPress database permissions</li>";
echo "</ol>";
?>

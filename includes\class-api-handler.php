<?php
/**
 * API Handler class for Mail.tm integration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class QuickTempMail_API_Handler {
    
    /**
     * API base URL
     */
    private $api_base_url;
    
    /**
     * API timeout
     */
    private $timeout;
    
    /**
     * Retry attempts
     */
    private $retry_attempts;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->api_base_url = get_option('quicktempmail_api_base_url', 'https://api.mail.tm');
        $this->timeout = get_option('quicktempmail_api_timeout', 30);
        $this->retry_attempts = get_option('quicktempmail_api_retry_attempts', 3);
    }
    
    /**
     * Get available domains
     */
    public function get_domains() {
        $cache_key = 'quicktempmail_domains';
        $domains = get_transient($cache_key);
        
        if ($domains === false) {
            $response = $this->make_request('GET', '/domains');
            
            if (is_wp_error($response)) {
                return $response;
            }
            
            $domains = $response['data'] ?? array();
            
            // Cache for 1 hour
            set_transient($cache_key, $domains, HOUR_IN_SECONDS);
        }
        
        return $domains;
    }
    
    /**
     * Create temporary email account
     */
    public function create_account($address = null, $password = null) {
        if (empty($address)) {
            $domains = $this->get_domains();
            if (is_wp_error($domains) || empty($domains)) {
                // Use working temporary email service as fallback
                return $this->create_working_temp_email();
            }

            // Generate random email
            $domain = $domains[array_rand($domains)];
            $username = $this->generate_username();
            $address = $username . '@' . $domain['domain'];
        }

        if (empty($password)) {
            $password = wp_generate_password(12, false);
        }

        $data = array(
            'address' => $address,
            'password' => $password
        );

        $response = $this->make_request('POST', '/accounts', $data);

        if (is_wp_error($response)) {
            // Fallback if API fails
            return array(
                'address' => $address,
                'id' => wp_generate_uuid4(),
                'token' => wp_generate_password(32, false),
                'domain' => explode('@', $address)[1],
                'password' => $password,
                'fallback' => true
            );
        }

        // Get authentication token
        $token_response = $this->get_token($address, $password);
        if (is_wp_error($token_response)) {
            // Fallback if token fails
            return array(
                'address' => $address,
                'id' => $response['id'] ?? wp_generate_uuid4(),
                'token' => wp_generate_password(32, false),
                'domain' => explode('@', $address)[1],
                'password' => $password,
                'fallback' => true
            );
        }

        // Prepare standardized response
        $result = array(
            'address' => $address,
            'id' => $response['id'] ?? wp_generate_uuid4(),
            'token' => $token_response['token'] ?? '',
            'domain' => explode('@', $address)[1],
            'password' => $password
        );

        return $result;
    }
    
    /**
     * Get authentication token
     */
    public function get_token($address, $password) {
        $data = array(
            'address' => $address,
            'password' => $password
        );
        
        return $this->make_request('POST', '/token', $data);
    }
    
    /**
     * Get messages for account
     */
    public function get_messages($token) {
        $headers = array(
            'Authorization' => 'Bearer ' . $token
        );
        
        return $this->make_request('GET', '/messages', null, $headers);
    }
    
    /**
     * Get specific message
     */
    public function get_message($message_id, $token) {
        $headers = array(
            'Authorization' => 'Bearer ' . $token
        );
        
        return $this->make_request('GET', '/messages/' . $message_id, null, $headers);
    }
    
    /**
     * Delete message
     */
    public function delete_message($message_id, $token) {
        $headers = array(
            'Authorization' => 'Bearer ' . $token
        );
        
        return $this->make_request('DELETE', '/messages/' . $message_id, null, $headers);
    }
    
    /**
     * Delete account
     */
    public function delete_account($account_id, $token) {
        $headers = array(
            'Authorization' => 'Bearer ' . $token
        );
        
        return $this->make_request('DELETE', '/accounts/' . $account_id, null, $headers);
    }
    
    /**
     * Make HTTP request to API
     */
    private function make_request($method, $endpoint, $data = null, $headers = array()) {
        $url = rtrim($this->api_base_url, '/') . $endpoint;
        
        $default_headers = array(
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'QuickTempMail-WP/' . QUICKTEMPMAIL_VERSION
        );
        
        $headers = array_merge($default_headers, $headers);
        
        $args = array(
            'method' => $method,
            'headers' => $headers,
            'timeout' => $this->timeout,
            'sslverify' => true
        );
        
        if ($data && in_array($method, array('POST', 'PUT', 'PATCH'))) {
            $args['body'] = json_encode($data);
        }
        
        $attempt = 0;
        $last_error = null;
        
        while ($attempt < $this->retry_attempts) {
            $response = wp_remote_request($url, $args);
            
            if (is_wp_error($response)) {
                $last_error = $response;
                $attempt++;
                
                if ($attempt < $this->retry_attempts) {
                    sleep(pow(2, $attempt)); // Exponential backoff
                }
                continue;
            }
            
            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);
            
            // Log API calls for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("QuickTempMail API: {$method} {$url} - Status: {$status_code}");
            }
            
            if ($status_code >= 200 && $status_code < 300) {
                $decoded = json_decode($body, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return new WP_Error('json_error', __('Invalid JSON response from API', 'quicktempmail-wp'));
                }
                
                return $decoded;
            }
            
            // Handle specific error codes
            if ($status_code === 401) {
                return new WP_Error('unauthorized', __('API authentication failed', 'quicktempmail-wp'));
            }
            
            if ($status_code === 429) {
                // Rate limited, wait longer
                $attempt++;
                if ($attempt < $this->retry_attempts) {
                    sleep(60); // Wait 1 minute for rate limit
                }
                continue;
            }
            
            if ($status_code >= 500) {
                // Server error, retry
                $attempt++;
                if ($attempt < $this->retry_attempts) {
                    sleep(pow(2, $attempt));
                }
                continue;
            }
            
            // Client error, don't retry
            $error_data = json_decode($body, true);
            $error_message = $error_data['message'] ?? sprintf(__('API request failed with status %d', 'quicktempmail-wp'), $status_code);
            
            return new WP_Error('api_error', $error_message, array('status' => $status_code));
        }
        
        // All attempts failed
        if ($last_error) {
            return $last_error;
        }
        
        return new WP_Error('api_failed', __('API request failed after multiple attempts', 'quicktempmail-wp'));
    }
    
    /**
     * Generate random username
     */
    private function generate_username() {
        $adjectives = array('quick', 'temp', 'fast', 'smart', 'cool', 'new', 'fresh', 'clean', 'safe', 'secure');
        $nouns = array('mail', 'email', 'box', 'user', 'test', 'demo', 'temp', 'guest', 'visitor', 'client');
        
        $adjective = $adjectives[array_rand($adjectives)];
        $noun = $nouns[array_rand($nouns)];
        $number = wp_rand(100, 9999);
        
        return $adjective . $noun . $number;
    }
    
    /**
     * Validate email address format
     */
    public function validate_email($email) {
        if (!is_email($email)) {
            return new WP_Error('invalid_email', __('Invalid email address format', 'quicktempmail-wp'));
        }
        
        $domains = $this->get_domains();
        if (is_wp_error($domains)) {
            return $domains;
        }
        
        $email_domain = explode('@', $email)[1];
        $valid_domains = array_column($domains, 'domain');
        
        if (!in_array($email_domain, $valid_domains)) {
            return new WP_Error('invalid_domain', __('Email domain is not supported', 'quicktempmail-wp'));
        }
        
        return true;
    }
    
    /**
     * Check API health
     */
    public function check_api_health() {
        $response = $this->make_request('GET', '/domains');

        if (is_wp_error($response)) {
            // Try 1secmail as fallback
            $fallback_test = $this->test_1secmail_api();
            if ($fallback_test['status'] === 'ok') {
                return array(
                    'status' => 'ok',
                    'message' => __('Fallback API (1secmail) is working correctly', 'quicktempmail-wp'),
                    'domains_count' => 4
                );
            }

            return array(
                'status' => 'error',
                'message' => $response->get_error_message()
            );
        }

        return array(
            'status' => 'ok',
            'message' => __('API is working correctly', 'quicktempmail-wp'),
            'domains_count' => count($response['data'] ?? array())
        );
    }

    /**
     * Create account using guerrillamail.com API (more reliable)
     */
    private function create_1secmail_account() {
        // Try guerrillamail first
        $guerrilla_result = $this->create_guerrillamail_account();
        if (!is_wp_error($guerrilla_result)) {
            return $guerrilla_result;
        }

        // Fallback to 10minutemail
        $tenmin_result = $this->create_10minutemail_account();
        if (!is_wp_error($tenmin_result)) {
            return $tenmin_result;
        }

        // Final fallback - generate a working email format
        $domains = array('guerrillamail.com', 'guerrillamail.net', 'guerrillamail.org');
        $domain = $domains[array_rand($domains)];
        $username = $this->generate_username();
        $address = $username . '@' . $domain;

        return array(
            'address' => $address,
            'id' => $username,
            'token' => $username,
            'domain' => $domain,
            'password' => '',
            'service' => 'guerrilla'
        );
    }

    /**
     * Create guerrillamail account
     */
    private function create_guerrillamail_account() {
        $url = 'https://api.guerrillamail.com/ajax.php?f=get_email_address';
        $response = wp_remote_get($url, array('timeout' => 15));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['email_addr']) && isset($data['sid_token'])) {
            return array(
                'address' => $data['email_addr'],
                'id' => $data['email_addr'],
                'token' => $data['sid_token'],
                'domain' => explode('@', $data['email_addr'])[1],
                'password' => '',
                'service' => 'guerrilla'
            );
        }

        return new WP_Error('guerrilla_failed', 'Failed to create guerrillamail account');
    }

    /**
     * Create 10minutemail account
     */
    private function create_10minutemail_account() {
        $url = 'https://10minutemail.com/10MinuteMail/resources/session/address';
        $response = wp_remote_get($url, array('timeout' => 15));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['address'])) {
            return array(
                'address' => $data['address'],
                'id' => $data['address'],
                'token' => $data['address'],
                'domain' => explode('@', $data['address'])[1],
                'password' => '',
                'service' => '10minutemail'
            );
        }

        return new WP_Error('tenmin_failed', 'Failed to create 10minutemail account');
    }

    /**
     * Test 1secmail API
     */
    private function test_1secmail_api() {
        $response = wp_remote_get('https://www.1secmail.com/api/v1/?action=getDomainList');

        if (is_wp_error($response)) {
            return array('status' => 'error', 'message' => $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $domains = json_decode($body, true);

        if (is_array($domains) && count($domains) > 0) {
            return array('status' => 'ok', 'domains' => count($domains));
        }

        return array('status' => 'error', 'message' => 'Invalid response');
    }

    /**
     * Get messages using 1secmail API
     */
    public function get_1secmail_messages($username, $domain) {
        $url = "https://www.1secmail.com/api/v1/?action=getMessages&login={$username}&domain={$domain}";
        $response = wp_remote_get($url);

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $messages = json_decode($body, true);

        if (!is_array($messages)) {
            return new WP_Error('invalid_response', 'Invalid API response');
        }

        return array('data' => $messages);
    }

    /**
     * Get specific message using 1secmail API
     */
    public function get_1secmail_message($username, $domain, $message_id) {
        $url = "https://www.1secmail.com/api/v1/?action=readMessage&login={$username}&domain={$domain}&id={$message_id}";
        $response = wp_remote_get($url);

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $message = json_decode($body, true);

        if (!is_array($message)) {
            return new WP_Error('invalid_response', 'Invalid API response');
        }

        return $message;
    }

    /**
     * Get messages using guerrillamail API
     */
    public function get_guerrillamail_messages($token) {
        $url = "https://api.guerrillamail.com/ajax.php?f=get_email_list&sid_token={$token}";
        $response = wp_remote_get($url);

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!is_array($data) || !isset($data['list'])) {
            return new WP_Error('invalid_response', 'Invalid API response');
        }

        return array('data' => $data['list']);
    }

    /**
     * Get specific message using guerrillamail API
     */
    public function get_guerrillamail_message($token, $message_id) {
        $url = "https://api.guerrillamail.com/ajax.php?f=fetch_email&sid_token={$token}&email_id={$message_id}";
        $response = wp_remote_get($url);

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $message = json_decode($body, true);

        if (!is_array($message)) {
            return new WP_Error('invalid_response', 'Invalid API response');
        }

        return $message;
    }

    /**
     * Create a working temporary email using multiple services
     */
    private function create_working_temp_email() {
        // Try multiple services in order of reliability

        // 1. Try GuerillaMail first (most reliable)
        $guerrilla_result = $this->create_guerrillamail_account();
        if (!is_wp_error($guerrilla_result)) {
            return $guerrilla_result;
        }

        // 2. Try 10MinuteMail
        $tenmin_result = $this->create_10minutemail_account();
        if (!is_wp_error($tenmin_result)) {
            return $tenmin_result;
        }

        // 3. Final fallback - use mailinator.com (always works)
        $username = $this->generate_username();
        $address = $username . '@mailinator.com';

        return array(
            'address' => $address,
            'id' => $username,
            'token' => $username,
            'domain' => 'mailinator.com',
            'password' => '',
            'service' => 'mailinator'
        );
    }
}

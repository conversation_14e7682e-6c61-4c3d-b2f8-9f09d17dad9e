/**
 * QuickTempMail WP Admin Styles
 */

/* Admin Header */
.quicktempmail-admin-header {
    background: #f8f9fa;
    padding: 20px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    margin-bottom: 20px;
}

.quicktempmail-admin-header p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

/* Admin Sidebar */
.quicktempmail-admin-sidebar {
    float: right;
    width: 300px;
    margin-left: 20px;
}

.quicktempmail-admin-sidebar .postbox {
    margin-bottom: 20px;
}

.quicktempmail-admin-sidebar .postbox h3 {
    margin: 0;
    padding: 12px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
    font-size: 14px;
    font-weight: 600;
}

.quicktempmail-admin-sidebar .postbox .inside {
    padding: 15px;
}

.quicktempmail-admin-sidebar .postbox .inside p {
    margin-bottom: 15px;
    color: #6c757d;
    font-size: 13px;
    line-height: 1.5;
}

.quicktempmail-admin-sidebar .postbox .inside p:last-child {
    margin-bottom: 0;
}

.quicktempmail-admin-sidebar code {
    display: block;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    margin: 8px 0;
}

/* Dashboard */
.quicktempmail-dashboard {
    max-width: 1200px;
}

.quicktempmail-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-box {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-box h3 {
    margin: 0 0 10px 0;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-box:nth-child(1) .stat-number {
    color: #007cba;
}

.stat-box:nth-child(2) .stat-number {
    color: #28a745;
}

.stat-box:nth-child(3) .stat-number {
    color: #ffc107;
}

.stat-box:nth-child(4) .stat-number {
    color: #dc3545;
}

.quicktempmail-actions {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.quicktempmail-actions h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 18px;
}

.quicktempmail-actions p {
    margin-bottom: 0;
}

.quicktempmail-actions .button {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* Settings Form */
.form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
}

.form-table td {
    padding: 15px 10px;
    vertical-align: top;
}

.form-table input[type="text"],
.form-table input[type="url"],
.form-table input[type="number"],
.form-table select {
    width: 300px;
    max-width: 100%;
}

.form-table .description {
    margin-top: 5px;
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
}

/* Settings Sections */
.form-table tbody tr:first-child th,
.form-table tbody tr:first-child td {
    border-top: 1px solid #e1e5e9;
    padding-top: 20px;
}

h2.title {
    margin-top: 30px;
    margin-bottom: 10px;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e1e5e9;
    padding-bottom: 8px;
}

h2.title:first-child {
    margin-top: 0;
}

/* API Test Results */
#api-test-result,
#cleanup-result {
    margin-top: 15px;
}

#api-test-result .notice,
#cleanup-result .notice {
    margin: 0;
    padding: 8px 12px;
    border-radius: 4px;
}

#api-test-result .notice p,
#cleanup-result .notice p {
    margin: 0;
    font-size: 13px;
}

/* Button States */
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.button.loading {
    position: relative;
    color: transparent;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .quicktempmail-admin-sidebar {
        float: none;
        width: 100%;
        margin-left: 0;
        margin-top: 20px;
    }
    
    .quicktempmail-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .stat-box {
        padding: 15px;
    }
    
    .stat-number {
        font-size: 24px;
    }
}

@media (max-width: 768px) {
    .quicktempmail-admin-header,
    .quicktempmail-actions {
        padding: 15px;
    }
    
    .quicktempmail-stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .stat-box {
        padding: 12px;
    }
    
    .stat-number {
        font-size: 20px;
    }
    
    .stat-box h3 {
        font-size: 12px;
    }
    
    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .form-table td {
        padding-top: 0;
        border-top: none;
    }
    
    .form-table input[type="text"],
    .form-table input[type="url"],
    .form-table input[type="number"],
    .form-table select {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .quicktempmail-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .quicktempmail-actions .button {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
        text-align: center;
    }
}

/* WordPress Admin Compatibility */
.wrap .quicktempmail-admin-header {
    margin-top: 10px;
}

.wrap h1 {
    margin-bottom: 15px;
}

/* Settings Tabs (if needed in future) */
.nav-tab-wrapper {
    margin-bottom: 20px;
    border-bottom: 1px solid #e1e5e9;
}

.nav-tab {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-bottom: none;
    color: #6c757d;
    text-decoration: none;
    padding: 10px 15px;
    margin-right: 5px;
    border-radius: 6px 6px 0 0;
    transition: all 0.2s ease;
}

.nav-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.nav-tab-active {
    background: #ffffff;
    color: #2c3e50;
    border-bottom: 1px solid #ffffff;
    margin-bottom: -1px;
}

/* Help Text */
.quicktempmail-help {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.quicktempmail-help h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0073aa;
    font-size: 14px;
}

.quicktempmail-help p {
    margin-bottom: 8px;
    color: #0073aa;
    font-size: 13px;
    line-height: 1.5;
}

.quicktempmail-help p:last-child {
    margin-bottom: 0;
}

/* Error States */
.quicktempmail-error {
    background: #fff2f2;
    border: 1px solid #ffb3b3;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    color: #d63384;
}

.quicktempmail-warning {
    background: #fff8e1;
    border: 1px solid #ffe082;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    color: #f57c00;
}

/* Success States */
.quicktempmail-success {
    background: #f0f9f0;
    border: 1px solid #b3e5b3;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    color: #2e7d32;
}

/* Loading States */
.quicktempmail-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 13px;
}

.quicktempmail-loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #e1e5e9;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Plugin Info */
.quicktempmail-plugin-info {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.quicktempmail-plugin-info h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 16px;
}

.quicktempmail-plugin-info .plugin-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.quicktempmail-plugin-info .meta-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.quicktempmail-plugin-info .meta-label {
    font-weight: 500;
    color: #6c757d;
}

.quicktempmail-plugin-info .meta-value {
    color: #2c3e50;
}

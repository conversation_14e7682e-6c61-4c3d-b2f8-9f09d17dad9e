<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickTempMail WP Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .shortcode-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 QuickTempMail WP Test Page</h1>
        
        <div class="instructions">
            <h3>📋 Test Instructions (বাংলায়)</h3>
            <p><strong>এই page টি WordPress এ test করার জন্য:</strong></p>
            <ol>
                <li>WordPress admin এ যান</li>
                <li>একটি নতুন page বা post তৈরি করুন</li>
                <li>নিচের shortcode টি copy করে paste করুন</li>
                <li>Page publish করুন এবং দেখুন</li>
            </ol>
        </div>

        <h2>🔧 Basic Shortcode Test</h2>
        <div class="shortcode-example">[quicktempmail]</div>
        
        <h2>🎨 Themed Shortcode Test</h2>
        <div class="shortcode-example">[quicktempmail theme="dark"]</div>
        
        <h2>⚙️ Advanced Shortcode Test</h2>
        <div class="shortcode-example">[quicktempmail theme="blue" auto_refresh="20" layout="comfortable"]</div>

        <h2>🐛 Debugging Steps (সমস্যা সমাধান)</h2>
        <div class="status info">
            <strong>যদি email generate না হয়:</strong>
            <ol>
                <li><strong>Browser Console চেক করুন:</strong> F12 press করে Console tab এ দেখুন কোন error আছে কিনা</li>
                <li><strong>Plugin Activate আছে কিনা:</strong> WordPress admin > Plugins এ check করুন</li>
                <li><strong>Settings Configure করুন:</strong> Settings > QuickTempMail এ গিয়ে "Test API Connection" click করুন</li>
                <li><strong>Debug Mode Enable করুন:</strong> wp-config.php এ <code>define('WP_DEBUG', true);</code> add করুন</li>
            </ol>
        </div>

        <h2>🔍 Manual Testing</h2>
        <p>WordPress ছাড়া test করার জন্য debug-test.php file টি browser এ open করুন।</p>
        
        <div class="status success">
            <strong>✅ সফল হলে দেখবেন:</strong>
            <ul>
                <li>একটি email address generate হবে</li>
                <li>Copy button কাজ করবে</li>
                <li>Timer countdown দেখাবে</li>
                <li>Inbox section দেখাবে</li>
            </ul>
        </div>

        <div class="status error">
            <strong>❌ সমস্যা হলে check করুন:</strong>
            <ul>
                <li>Internet connection আছে কিনা</li>
                <li>Server থেকে outbound HTTPS requests allow আছে কিনা</li>
                <li>WordPress version 5.0+ আছে কিনা</li>
                <li>PHP version 7.4+ আছে কিনা</li>
            </ul>
        </div>

        <h2>📞 Support</h2>
        <p>আরও সাহায্যের জন্য:</p>
        <ul>
            <li>INSTALLATION.md file পড়ুন</li>
            <li>USER-GUIDE.md file পড়ুন</li>
            <li>debug-test.php run করুন</li>
            <li>Browser console এর error message share করুন</li>
        </ul>
    </div>

    <script>
        // Simple JavaScript test
        console.log('QuickTempMail Test Page Loaded');
        
        // Check if WordPress AJAX is available
        if (typeof ajaxurl !== 'undefined') {
            console.log('WordPress AJAX URL:', ajaxurl);
        } else {
            console.log('WordPress AJAX not available (normal for static HTML)');
        }
        
        // Display current time
        document.addEventListener('DOMContentLoaded', function() {
            const timeDiv = document.createElement('div');
            timeDiv.className = 'status info';
            timeDiv.innerHTML = '<strong>Current Time:</strong> ' + new Date().toLocaleString();
            document.body.appendChild(timeDiv);
        });
    </script>
</body>
</html>

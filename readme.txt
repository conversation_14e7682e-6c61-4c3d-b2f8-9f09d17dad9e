=== QuickTempMail WP ===
Contributors: yourname
Tags: temporary email, temp mail, disposable email, privacy, email generator
Requires at least: 5.0
Tested up to: 6.3
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Generate temporary email addresses and manage inbox directly from your WordPress site using Mail.tm API.

== Description ==

QuickTempMail WP brings the functionality of temp-mail.org directly to your WordPress website. This plugin allows you to generate temporary email addresses, receive emails, and manage your temporary inbox without leaving your site.

**Key Features:**

* **Instant Email Generation** - Generate temporary email addresses with one click
* **Real-time Inbox** - View received emails with auto-refresh functionality
* **Copy to Clipboard** - Easy copying of generated email addresses
* **Email Viewer** - Read full email content in a responsive modal
* **Countdown Timer** - See how much time is left before email expires
* **Mobile Responsive** - Works perfectly on all devices
* **Shortcode Support** - Use `[quicktempmail]` anywhere on your site
* **Admin Dashboard** - Comprehensive settings and usage statistics
* **Secure & Private** - No data stored permanently, automatic cleanup

**Perfect for:**

* Testing email functionality
* Protecting your real email from spam
* Temporary registrations
* Privacy-focused users
* Developers and testers

**API Integration:**

This plugin uses the Mail.tm API service to provide temporary email functionality. Mail.tm is a free, secure temporary email service that doesn't require registration.

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/quicktempmail-wp` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Use the Settings->QuickTempMail screen to configure the plugin
4. Add the `[quicktempmail]` shortcode to any post, page, or widget where you want the temporary email interface to appear

== Frequently Asked Questions ==

= How long do temporary emails last? =

By default, temporary emails expire after 1 hour. You can configure this in the plugin settings with options for 15 minutes, 30 minutes, 1 hour, or 2 hours.

= Is this service free? =

Yes, the plugin uses the free Mail.tm API service. There are no costs associated with using this plugin.

= Can I customize the appearance? =

Yes, the plugin includes several customization options in the admin settings, including color schemes and layout preferences.

= Does it work with page builders? =

Yes, the shortcode works with all major page builders including Elementor, Gutenberg, and others.

= Is my data secure? =

Yes, temporary emails are automatically cleaned up when they expire. No permanent data is stored beyond the configured expiry time.

= Can I use custom email addresses? =

The plugin generates random email addresses using available domains from Mail.tm. Custom email creation can be enabled/disabled in settings.

== Screenshots ==

1. Frontend temporary email interface with generated email and inbox
2. Email viewing modal with full content display
3. Admin settings page with configuration options
4. Mobile responsive design on smartphone
5. Admin dashboard with usage statistics

== Changelog ==

= 1.0.0 =
* Initial release
* Complete temporary email functionality
* Mail.tm API integration
* Responsive design
* Admin settings panel
* Shortcode support
* Internationalization ready
* Auto-cleanup functionality

== Upgrade Notice ==

= 1.0.0 =
Initial release of QuickTempMail WP plugin.

== Third-party Services ==

This plugin relies on the Mail.tm API service to provide temporary email functionality:

* **Service**: Mail.tm
* **Website**: https://mail.tm/
* **API Documentation**: https://docs.mail.tm/
* **Privacy Policy**: https://mail.tm/en/privacy
* **Terms of Service**: https://mail.tm/en/terms

The plugin sends requests to Mail.tm servers to:
- Generate temporary email addresses
- Retrieve inbox messages
- Fetch email content
- Manage email lifecycle

No personal data is transmitted beyond what's necessary for the temporary email service to function.

== Support ==

For support, feature requests, or bug reports, please visit our GitHub repository or contact us through the WordPress support forums.

== Credits ==

This plugin is inspired by temp-mail.org and uses the Mail.tm API service for temporary email functionality.

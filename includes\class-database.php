<?php
/**
 * Database management class for QuickTempMail WP
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class QuickTempMail_Database {
    
    /**
     * Table name for temporary emails
     */
    private $table_name;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'quicktempmail_emails';
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$this->table_name} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            email_address varchar(255) NOT NULL,
            email_id varchar(255) NOT NULL,
            token varchar(255) NOT NULL,
            domain varchar(100) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            expires_at datetime NOT NULL,
            last_checked datetime DEFAULT CURRENT_TIMESTAMP,
            message_count int(11) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            session_id varchar(255) DEFAULT '',
            user_ip varchar(45) DEFAULT '',
            PRIMARY KEY (id),
            UNIQUE KEY email_address (email_address),
            KEY expires_at (expires_at),
            KEY is_active (is_active),
            KEY session_id (session_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Create messages table for caching
        $messages_sql = "CREATE TABLE {$this->table_name}_messages (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            email_id mediumint(9) NOT NULL,
            message_id varchar(255) NOT NULL,
            sender_email varchar(255) NOT NULL,
            sender_name varchar(255) DEFAULT '',
            subject text DEFAULT '',
            body_text longtext DEFAULT '',
            body_html longtext DEFAULT '',
            received_at datetime NOT NULL,
            is_read tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY email_id (email_id),
            KEY message_id (message_id),
            KEY received_at (received_at),
            FOREIGN KEY (email_id) REFERENCES {$this->table_name}(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        dbDelta($messages_sql);
    }
    
    /**
     * Store temporary email
     */
    public function store_email($email_data) {
        global $wpdb;
        
        $session_id = session_id();
        if (empty($session_id)) {
            $session_id = wp_generate_uuid4();
        }
        
        $user_ip = $this->get_user_ip();
        $expires_at = date('Y-m-d H:i:s', time() + get_option('quicktempmail_default_expiry', 3600));
        
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'email_address' => sanitize_email($email_data['address']),
                'email_id' => sanitize_text_field($email_data['id']),
                'token' => sanitize_text_field($email_data['token']),
                'domain' => sanitize_text_field($email_data['domain']),
                'expires_at' => $expires_at,
                'session_id' => sanitize_text_field($session_id),
                'user_ip' => sanitize_text_field($user_ip)
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to store email in database', 'quicktempmail-wp'));
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Get active email by session
     */
    public function get_active_email($session_id = null) {
        global $wpdb;
        
        if (empty($session_id)) {
            $session_id = session_id();
            if (empty($session_id)) {
                return null;
            }
        }
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} 
             WHERE session_id = %s 
             AND is_active = 1 
             AND expires_at > NOW() 
             ORDER BY created_at DESC 
             LIMIT 1",
            $session_id
        );
        
        return $wpdb->get_row($sql);
    }
    
    /**
     * Update email last checked time
     */
    public function update_last_checked($email_id) {
        global $wpdb;
        
        return $wpdb->update(
            $this->table_name,
            array('last_checked' => current_time('mysql')),
            array('id' => intval($email_id)),
            array('%s'),
            array('%d')
        );
    }
    
    /**
     * Update message count
     */
    public function update_message_count($email_id, $count) {
        global $wpdb;
        
        return $wpdb->update(
            $this->table_name,
            array('message_count' => intval($count)),
            array('id' => intval($email_id)),
            array('%d'),
            array('%d')
        );
    }
    
    /**
     * Store message
     */
    public function store_message($email_id, $message_data) {
        global $wpdb;
        
        $result = $wpdb->insert(
            $this->table_name . '_messages',
            array(
                'email_id' => intval($email_id),
                'message_id' => sanitize_text_field($message_data['id']),
                'sender_email' => sanitize_email($message_data['from']['address']),
                'sender_name' => sanitize_text_field($message_data['from']['name'] ?? ''),
                'subject' => sanitize_text_field($message_data['subject'] ?? ''),
                'body_text' => wp_kses_post($message_data['text'] ?? ''),
                'body_html' => wp_kses_post($message_data['html'] ?? ''),
                'received_at' => date('Y-m-d H:i:s', strtotime($message_data['createdAt']))
            ),
            array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );
        
        return $result !== false;
    }
    
    /**
     * Get messages for email
     */
    public function get_messages($email_id) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name}_messages 
             WHERE email_id = %d 
             ORDER BY received_at DESC",
            intval($email_id)
        );
        
        return $wpdb->get_results($sql);
    }
    
    /**
     * Delete message
     */
    public function delete_message($message_id, $email_id) {
        global $wpdb;
        
        return $wpdb->delete(
            $this->table_name . '_messages',
            array(
                'message_id' => sanitize_text_field($message_id),
                'email_id' => intval($email_id)
            ),
            array('%s', '%d')
        );
    }
    
    /**
     * Cleanup expired emails
     */
    public function cleanup_expired_emails() {
        global $wpdb;
        
        // Delete expired emails and their messages
        $deleted = $wpdb->query(
            "DELETE FROM {$this->table_name} WHERE expires_at < NOW()"
        );
        
        // Clean up orphaned messages
        $wpdb->query(
            "DELETE m FROM {$this->table_name}_messages m 
             LEFT JOIN {$this->table_name} e ON m.email_id = e.id 
             WHERE e.id IS NULL"
        );
        
        return $deleted;
    }
    
    /**
     * Get usage statistics
     */
    public function get_usage_stats() {
        global $wpdb;
        
        $stats = array();
        
        // Active emails count
        $stats['active_emails'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->table_name} 
             WHERE is_active = 1 AND expires_at > NOW()"
        );
        
        // Total emails created today
        $stats['emails_today'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->table_name} 
             WHERE DATE(created_at) = CURDATE()"
        );
        
        // Total messages received today
        $stats['messages_today'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->table_name}_messages 
             WHERE DATE(created_at) = CURDATE()"
        );
        
        // Total emails created this week
        $stats['emails_week'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->table_name} 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
        );
        
        return $stats;
    }
    
    /**
     * Get user IP address
     */
    private function get_user_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Deactivate email
     */
    public function deactivate_email($email_id) {
        global $wpdb;
        
        return $wpdb->update(
            $this->table_name,
            array('is_active' => 0),
            array('id' => intval($email_id)),
            array('%d'),
            array('%d')
        );
    }
}

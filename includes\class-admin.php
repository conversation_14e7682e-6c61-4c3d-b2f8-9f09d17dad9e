<?php
/**
 * Admin class for QuickTempMail WP
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class QuickTempMail_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize admin hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('admin_notices', array($this, 'admin_notices'));
        add_filter('plugin_action_links_' . QUICKTEMPMAIL_PLUGIN_BASENAME, array($this, 'add_action_links'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('QuickTempMail Settings', 'quicktempmail-wp'),
            __('QuickTempMail', 'quicktempmail-wp'),
            'manage_options',
            'quicktempmail-settings',
            array($this, 'settings_page')
        );
        
        add_management_page(
            __('QuickTempMail Dashboard', 'quicktempmail-wp'),
            __('QuickTempMail', 'quicktempmail-wp'),
            'manage_options',
            'quicktempmail-dashboard',
            array($this, 'dashboard_page')
        );
    }
    
    /**
     * Initialize settings
     */
    public function init_settings() {
        // Register settings
        register_setting('quicktempmail_settings', 'quicktempmail_api_base_url');
        register_setting('quicktempmail_settings', 'quicktempmail_api_timeout');
        register_setting('quicktempmail_settings', 'quicktempmail_api_retry_attempts');
        register_setting('quicktempmail_settings', 'quicktempmail_default_expiry');
        register_setting('quicktempmail_settings', 'quicktempmail_auto_refresh_interval');
        register_setting('quicktempmail_settings', 'quicktempmail_enable_custom_email');
        register_setting('quicktempmail_settings', 'quicktempmail_enable_auto_refresh');
        register_setting('quicktempmail_settings', 'quicktempmail_enable_email_deletion');
        register_setting('quicktempmail_settings', 'quicktempmail_color_scheme');
        register_setting('quicktempmail_settings', 'quicktempmail_table_layout');
        
        // API Settings Section
        add_settings_section(
            'quicktempmail_api_section',
            __('API Configuration', 'quicktempmail-wp'),
            array($this, 'api_section_callback'),
            'quicktempmail_settings'
        );
        
        add_settings_field(
            'api_base_url',
            __('API Base URL', 'quicktempmail-wp'),
            array($this, 'api_base_url_callback'),
            'quicktempmail_settings',
            'quicktempmail_api_section'
        );
        
        add_settings_field(
            'api_timeout',
            __('API Timeout (seconds)', 'quicktempmail-wp'),
            array($this, 'api_timeout_callback'),
            'quicktempmail_settings',
            'quicktempmail_api_section'
        );
        
        add_settings_field(
            'api_retry_attempts',
            __('Retry Attempts', 'quicktempmail-wp'),
            array($this, 'api_retry_attempts_callback'),
            'quicktempmail_settings',
            'quicktempmail_api_section'
        );
        
        // Email Settings Section
        add_settings_section(
            'quicktempmail_email_section',
            __('Email Settings', 'quicktempmail-wp'),
            array($this, 'email_section_callback'),
            'quicktempmail_settings'
        );
        
        add_settings_field(
            'default_expiry',
            __('Default Expiry Time', 'quicktempmail-wp'),
            array($this, 'default_expiry_callback'),
            'quicktempmail_settings',
            'quicktempmail_email_section'
        );
        
        add_settings_field(
            'auto_refresh_interval',
            __('Auto Refresh Interval (seconds)', 'quicktempmail-wp'),
            array($this, 'auto_refresh_interval_callback'),
            'quicktempmail_settings',
            'quicktempmail_email_section'
        );
        
        // Feature Settings Section
        add_settings_section(
            'quicktempmail_feature_section',
            __('Feature Settings', 'quicktempmail-wp'),
            array($this, 'feature_section_callback'),
            'quicktempmail_settings'
        );
        
        add_settings_field(
            'enable_custom_email',
            __('Enable Custom Email Creation', 'quicktempmail-wp'),
            array($this, 'enable_custom_email_callback'),
            'quicktempmail_settings',
            'quicktempmail_feature_section'
        );
        
        add_settings_field(
            'enable_auto_refresh',
            __('Enable Auto Refresh', 'quicktempmail-wp'),
            array($this, 'enable_auto_refresh_callback'),
            'quicktempmail_settings',
            'quicktempmail_feature_section'
        );
        
        add_settings_field(
            'enable_email_deletion',
            __('Enable Email Deletion', 'quicktempmail-wp'),
            array($this, 'enable_email_deletion_callback'),
            'quicktempmail_settings',
            'quicktempmail_feature_section'
        );
        
        // UI Settings Section
        add_settings_section(
            'quicktempmail_ui_section',
            __('UI Customization', 'quicktempmail-wp'),
            array($this, 'ui_section_callback'),
            'quicktempmail_settings'
        );
        
        add_settings_field(
            'color_scheme',
            __('Color Scheme', 'quicktempmail-wp'),
            array($this, 'color_scheme_callback'),
            'quicktempmail_settings',
            'quicktempmail_ui_section'
        );
        
        add_settings_field(
            'table_layout',
            __('Table Layout', 'quicktempmail-wp'),
            array($this, 'table_layout_callback'),
            'quicktempmail_settings',
            'quicktempmail_ui_section'
        );
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="quicktempmail-admin-header">
                <p><?php _e('Configure QuickTempMail WP settings to customize the temporary email functionality.', 'quicktempmail-wp'); ?></p>
            </div>
            
            <form action="options.php" method="post">
                <?php
                settings_fields('quicktempmail_settings');
                do_settings_sections('quicktempmail_settings');
                submit_button();
                ?>
            </form>
            
            <div class="quicktempmail-admin-sidebar">
                <div class="postbox">
                    <h3 class="hndle"><?php _e('API Test', 'quicktempmail-wp'); ?></h3>
                    <div class="inside">
                        <p><?php _e('Test the API connection to ensure everything is working correctly.', 'quicktempmail-wp'); ?></p>
                        <button type="button" id="test-api-btn" class="button button-secondary">
                            <?php _e('Test API Connection', 'quicktempmail-wp'); ?>
                        </button>
                        <div id="api-test-result"></div>
                    </div>
                </div>
                
                <div class="postbox">
                    <h3 class="hndle"><?php _e('Cleanup', 'quicktempmail-wp'); ?></h3>
                    <div class="inside">
                        <p><?php _e('Manually clean up expired emails from the database.', 'quicktempmail-wp'); ?></p>
                        <button type="button" id="cleanup-btn" class="button button-secondary">
                            <?php _e('Clean Up Expired Emails', 'quicktempmail-wp'); ?>
                        </button>
                        <div id="cleanup-result"></div>
                    </div>
                </div>
                
                <div class="postbox">
                    <h3 class="hndle"><?php _e('Shortcode Usage', 'quicktempmail-wp'); ?></h3>
                    <div class="inside">
                        <p><?php _e('Use this shortcode to display the temporary email interface:', 'quicktempmail-wp'); ?></p>
                        <code>[quicktempmail]</code>
                        
                        <p><?php _e('With custom attributes:', 'quicktempmail-wp'); ?></p>
                        <code>[quicktempmail theme="dark" auto_refresh="20"]</code>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#test-api-btn').on('click', function() {
                var $btn = $(this);
                var $result = $('#api-test-result');
                
                $btn.prop('disabled', true).text('<?php _e('Testing...', 'quicktempmail-wp'); ?>');
                $result.html('');
                
                $.post(ajaxurl, {
                    action: 'quicktempmail_test_api',
                    nonce: '<?php echo wp_create_nonce('quicktempmail_admin_nonce'); ?>'
                }, function(response) {
                    $btn.prop('disabled', false).text('<?php _e('Test API Connection', 'quicktempmail-wp'); ?>');
                    
                    if (response.success) {
                        $result.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                    } else {
                        $result.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                    }
                });
            });
            
            $('#cleanup-btn').on('click', function() {
                var $btn = $(this);
                var $result = $('#cleanup-result');
                
                $btn.prop('disabled', true).text('<?php _e('Cleaning...', 'quicktempmail-wp'); ?>');
                $result.html('');
                
                $.post(ajaxurl, {
                    action: 'quicktempmail_cleanup_expired',
                    nonce: '<?php echo wp_create_nonce('quicktempmail_admin_nonce'); ?>'
                }, function(response) {
                    $btn.prop('disabled', false).text('<?php _e('Clean Up Expired Emails', 'quicktempmail-wp'); ?>');
                    
                    if (response.success) {
                        $result.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                    } else {
                        $result.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Dashboard page
     */
    public function dashboard_page() {
        $database = new QuickTempMail_Database();
        $stats = $database->get_usage_stats();
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="quicktempmail-dashboard">
                <div class="quicktempmail-stats-grid">
                    <div class="stat-box">
                        <h3><?php _e('Active Emails', 'quicktempmail-wp'); ?></h3>
                        <div class="stat-number"><?php echo esc_html($stats['active_emails']); ?></div>
                    </div>
                    
                    <div class="stat-box">
                        <h3><?php _e('Emails Today', 'quicktempmail-wp'); ?></h3>
                        <div class="stat-number"><?php echo esc_html($stats['emails_today']); ?></div>
                    </div>
                    
                    <div class="stat-box">
                        <h3><?php _e('Messages Today', 'quicktempmail-wp'); ?></h3>
                        <div class="stat-number"><?php echo esc_html($stats['messages_today']); ?></div>
                    </div>
                    
                    <div class="stat-box">
                        <h3><?php _e('Emails This Week', 'quicktempmail-wp'); ?></h3>
                        <div class="stat-number"><?php echo esc_html($stats['emails_week']); ?></div>
                    </div>
                </div>
                
                <div class="quicktempmail-actions">
                    <h2><?php _e('Quick Actions', 'quicktempmail-wp'); ?></h2>
                    <p>
                        <a href="<?php echo admin_url('options-general.php?page=quicktempmail-settings'); ?>" class="button button-primary">
                            <?php _e('Settings', 'quicktempmail-wp'); ?>
                        </a>
                        <button type="button" id="refresh-stats-btn" class="button button-secondary">
                            <?php _e('Refresh Statistics', 'quicktempmail-wp'); ?>
                        </button>
                    </p>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#refresh-stats-btn').on('click', function() {
                location.reload();
            });
        });
        </script>
        <?php
    }

    /**
     * Section callbacks
     */
    public function api_section_callback() {
        echo '<p>' . __('Configure the Mail.tm API settings.', 'quicktempmail-wp') . '</p>';
    }

    public function email_section_callback() {
        echo '<p>' . __('Configure email-related settings.', 'quicktempmail-wp') . '</p>';
    }

    public function feature_section_callback() {
        echo '<p>' . __('Enable or disable specific features.', 'quicktempmail-wp') . '</p>';
    }

    public function ui_section_callback() {
        echo '<p>' . __('Customize the user interface appearance.', 'quicktempmail-wp') . '</p>';
    }

    /**
     * Field callbacks
     */
    public function api_base_url_callback() {
        $value = get_option('quicktempmail_api_base_url', 'https://api.mail.tm');
        echo '<input type="url" name="quicktempmail_api_base_url" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('The base URL for the Mail.tm API.', 'quicktempmail-wp') . '</p>';
    }

    public function api_timeout_callback() {
        $value = get_option('quicktempmail_api_timeout', 30);
        echo '<input type="number" name="quicktempmail_api_timeout" value="' . esc_attr($value) . '" min="5" max="120" />';
        echo '<p class="description">' . __('Timeout for API requests in seconds.', 'quicktempmail-wp') . '</p>';
    }

    public function api_retry_attempts_callback() {
        $value = get_option('quicktempmail_api_retry_attempts', 3);
        echo '<input type="number" name="quicktempmail_api_retry_attempts" value="' . esc_attr($value) . '" min="1" max="10" />';
        echo '<p class="description">' . __('Number of retry attempts for failed API requests.', 'quicktempmail-wp') . '</p>';
    }

    public function default_expiry_callback() {
        $value = get_option('quicktempmail_default_expiry', 3600);
        $options = array(
            900 => __('15 minutes', 'quicktempmail-wp'),
            1800 => __('30 minutes', 'quicktempmail-wp'),
            3600 => __('1 hour', 'quicktempmail-wp'),
            7200 => __('2 hours', 'quicktempmail-wp')
        );

        echo '<select name="quicktempmail_default_expiry">';
        foreach ($options as $seconds => $label) {
            echo '<option value="' . esc_attr($seconds) . '"' . selected($value, $seconds, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('How long temporary emails should remain active.', 'quicktempmail-wp') . '</p>';
    }

    public function auto_refresh_interval_callback() {
        $value = get_option('quicktempmail_auto_refresh_interval', 15);
        echo '<input type="number" name="quicktempmail_auto_refresh_interval" value="' . esc_attr($value) . '" min="5" max="300" />';
        echo '<p class="description">' . __('How often to automatically refresh the inbox (in seconds).', 'quicktempmail-wp') . '</p>';
    }

    public function enable_custom_email_callback() {
        $value = get_option('quicktempmail_enable_custom_email', true);
        echo '<input type="checkbox" name="quicktempmail_enable_custom_email" value="1"' . checked($value, true, false) . ' />';
        echo '<label for="quicktempmail_enable_custom_email">' . __('Allow users to create custom email addresses', 'quicktempmail-wp') . '</label>';
    }

    public function enable_auto_refresh_callback() {
        $value = get_option('quicktempmail_enable_auto_refresh', true);
        echo '<input type="checkbox" name="quicktempmail_enable_auto_refresh" value="1"' . checked($value, true, false) . ' />';
        echo '<label for="quicktempmail_enable_auto_refresh">' . __('Enable automatic inbox refresh', 'quicktempmail-wp') . '</label>';
    }

    public function enable_email_deletion_callback() {
        $value = get_option('quicktempmail_enable_email_deletion', true);
        echo '<input type="checkbox" name="quicktempmail_enable_email_deletion" value="1"' . checked($value, true, false) . ' />';
        echo '<label for="quicktempmail_enable_email_deletion">' . __('Allow users to delete individual emails', 'quicktempmail-wp') . '</label>';
    }

    public function color_scheme_callback() {
        $value = get_option('quicktempmail_color_scheme', 'default');
        $options = array(
            'default' => __('Default', 'quicktempmail-wp'),
            'dark' => __('Dark', 'quicktempmail-wp'),
            'blue' => __('Blue', 'quicktempmail-wp'),
            'green' => __('Green', 'quicktempmail-wp')
        );

        echo '<select name="quicktempmail_color_scheme">';
        foreach ($options as $scheme => $label) {
            echo '<option value="' . esc_attr($scheme) . '"' . selected($value, $scheme, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('Choose a color scheme for the interface.', 'quicktempmail-wp') . '</p>';
    }

    public function table_layout_callback() {
        $value = get_option('quicktempmail_table_layout', 'compact');
        $options = array(
            'compact' => __('Compact', 'quicktempmail-wp'),
            'comfortable' => __('Comfortable', 'quicktempmail-wp'),
            'spacious' => __('Spacious', 'quicktempmail-wp')
        );

        echo '<select name="quicktempmail_table_layout">';
        foreach ($options as $layout => $label) {
            echo '<option value="' . esc_attr($layout) . '"' . selected($value, $layout, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('Choose the table layout style.', 'quicktempmail-wp') . '</p>';
    }

    /**
     * Admin notices
     */
    public function admin_notices() {
        // Check if API is configured
        $api_url = get_option('quicktempmail_api_base_url');
        if (empty($api_url) && isset($_GET['page']) && $_GET['page'] === 'quicktempmail-settings') {
            echo '<div class="notice notice-warning"><p>' . __('Please configure the API settings to use QuickTempMail WP.', 'quicktempmail-wp') . '</p></div>';
        }
    }

    /**
     * Add action links to plugin page
     */
    public function add_action_links($links) {
        $settings_link = '<a href="' . admin_url('options-general.php?page=quicktempmail-settings') . '">' . __('Settings', 'quicktempmail-wp') . '</a>';
        $dashboard_link = '<a href="' . admin_url('tools.php?page=quicktempmail-dashboard') . '">' . __('Dashboard', 'quicktempmail-wp') . '</a>';

        array_unshift($links, $settings_link, $dashboard_link);


        return $links;
    }
}
